package com.qm.ep.sysres.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qm.ep.sysres.constant.SocketConstant;
import com.qm.ep.sysres.domain.dto.WebSocketDto;
import com.qm.tds.api.client.EpRedisClient;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.dynamic.utils.SpringContextUtils;
import com.qm.tds.mq.builder.DefaultDestination;
import com.qm.tds.mq.builder.DefaultTxMessage;
import com.qm.tds.mq.builder.MessageStruct;
import com.qm.tds.mq.constant.ExchangeType;
import com.qm.tds.mq.message.MessageSendStruct;
import com.qm.tds.mq.remote.MqFeignRemote;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

@Component
@Slf4j
@ServerEndpoint("/websocket/{userId}") //此注解相当于设置访问URL
public class WebSocketCServer {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private I18nUtil i18nUtil;
    private Session sessionObj;

    private String userId;

    private static final String REDIS_TOPIC_NAME = "socketHandler";

    @Value("${spring.application.name}")
    private String serviceName;
    @Autowired
    private MqFeignRemote mqFeignRemote;

    @Resource
    private EpRedisClient epRedisClient;

    @Value("${qm.websocket.mqSendFlag}")
    private boolean mqSendFlag;

    /**
     * 缓存 webSocket连接到单机服务class中（整体方案支持集群）
     */
    private static CopyOnWriteArraySet<WebSocketCServer> webSockets = new CopyOnWriteArraySet<>();
    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的session对象。
     */
    private static final ConcurrentHashMap<String, Session> sessionPool = new ConcurrentHashMap<>();


    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId) {
        try {
            if ( sessionPool.isEmpty() && !sessionPool.containsKey(userId)) {
                session.setMaxIdleTimeout(3600000);
                this.sessionObj = session;
                this.userId = userId;
                webSockets.add(this);
                sessionPool.put(userId, session);
                addOnlineCount();
                log.info("【websocket消息】有新的连接，总数为:" + webSockets.size());
            }

        } catch (Exception e) {
            log.info("---error--"+e.getMessage());
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        try {
            if (sessionPool.containsKey(userId)) {
                webSockets.remove(this);
                sessionPool.remove(this.userId);
                //从set中删除
                subOnlineCount();
                log.info("【websocket消息】连接断开，总数为:" + webSockets.size() + " 断开的用户为：" + this.userId);
            }
        } catch (Exception e) {
            log.info("---error--"+e.getMessage());
        }
    }

    public void clearWS() {
        try {
            webSockets.clear();
            sessionPool.clear();
            log.info("清除REDIS内所有用户....");
            String redisKey = redisUtils.keyBuilder("Socket", SocketConstant.SOCKET_USER, "*");
            redisUtils.del(redisUtils.getKeys(redisKey));
        } catch (Exception e) {
            log.info("---error--"+e.getMessage());
        }
    }

    public synchronized void addOnlineCount() {
        redisServiceInit();
        String redisKey = redisUtils.keyBuilder(SocketConstant.REDIS_KEY_PRE, SocketConstant.SOCKET_USER, userId);
        redisUtils.set(redisKey, userId);
    }

    public synchronized void subOnlineCount() {
        redisServiceInit();
        String redisKey = redisUtils.keyBuilder(SocketConstant.REDIS_KEY_PRE, SocketConstant.SOCKET_USER, userId);
        redisUtils.del(redisKey);
    }

    /**
     * 初始化 redis 服务
     */
    void redisServiceInit() {
        if (redisUtils == null) {
            redisUtils = SpringContextUtils.getBean(RedisUtils.class);
        }
    }

    /**
     * 清除失活链接
     */
    public void deleteCloseLink() {
        log.debug("-----------------------------检查失活链接:{}", sessionPool.size());
        sessionPool.forEach((k, v) -> {
            try {
                synchronized (v) {
                    v.getAsyncRemote().sendText("");
                }
            } catch (Exception e) {
                sessionPool.remove(k);
                log.info("---error--"+e.getMessage(), e);
            }
        });
    }

    /**
     * 服务端推送消息
     *
     * @param userId
     * @param message
     */
    public void pushMessage(String userId, String message) {
        Session session = sessionPool.get(userId);
        if (session != null && session.isOpen()) {
            try {
                log.info("【websocket消息MQ--------------------】 单点消息userId:" + userId + " message" + message);
                synchronized (session) {
                    session.getAsyncRemote().sendText(message);
                }
            } catch (Exception ex) {
                log.info("【websocket消息MQ--------------------】，消息通道中断，非正常退出！");
            }
        } else {
            log.debug("【websocket消息MQ-----------------------】 单点消息,没有找到userId：" + userId + "会话！");
        }
    }

    public void sendMessageByMq(WebSocketDto webSocketDto) {
        // type不对不发消息
        if (BootAppUtil.isNullOrEmpty(webSocketDto.getSendType()) || webSocketDto.getSendType() == 0) {
            return;
        }

        // 消息体对象
        MessageSendStruct messageSendStruct = new MessageSendStruct(
                DefaultTxMessage.builder()
                        //业务主键
                        .businessKey(UUID.randomUUID().toString())
                        //微服务名称
                        .businessModule(serviceName)
                        //发送内容
                        .content(MessageStruct.builder()
                                //请求头信息
                                .requestInfo(BootAppUtil.getLoginKey())
                                //发送需要处理的内容
                                .message(webSocketDto)
                                //微服务名称
                                .serviceName(serviceName).build())
                        .build(),
                //不是订阅模式（直接、广播、订阅） 不传入交换机名称、路由键  只有订阅模式才有交换机
                DefaultDestination.builder()
                        //交换机名称 供应商基础信息
                        .exchangeName(SocketConstant.WEBSOCKET_EXCHANGE)
                        //队列名称
                        .queueName("")
                        .expire("180000")
                        //路由键
                        .routingKey("")
                        //交换机类型
                        .exchangeType(ExchangeType.FANOUT)
                        .build());
        JsonResultVo mqRet = mqFeignRemote.sendRabbitMq(messageSendStruct);
        if (mqRet.getCode() != HttpServletResponse.SC_OK) {
            // "MQ消息分发失败。"
            String message = i18nUtil.getMessage("MSG.sysres.WebSocketCServer.mqSendFail");
            throw new QmException(message + mqRet.getMsg());
        }
    }

    /**
     * 服务器端推送消息
     */
    public void pushMessage(String message) {
        try {
            webSockets.forEach(ws -> ws.sessionObj.getAsyncRemote().sendText(message));
        } catch (Exception ex) {
            log.info("---error--"+"调用pushMessage异常 Exceptionmessage:" + ex.getMessage(), ex);
        }
    }

    private String buildLock(String params) {
        StringBuilder sb = new StringBuilder();
        sb.append(params);
        return sb.toString().intern();
    }

    @SuppressWarnings("squid:S1860")
    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("【websocket消息】收到前端心跳检测消息:" + message);
        JSONObject obj = new JSONObject();
        //业务类型
        obj.put(SocketConstant.MSG_CMD, SocketConstant.CMD_CHECK);
        //消息内容 "心跳响应"
        String heartbeatResponse = i18nUtil.getMessage("MSG.sysres.WebSocketCServer.heartbeatResponse");
        obj.put(SocketConstant.MSG_TXT, heartbeatResponse);
        String responseMsg = message;
        if (message.equals("ping"))
            responseMsg = "pong";
        String lockStr = buildLock(responseMsg);
        synchronized (lockStr) {
            try {
                session.getBasicRemote().sendText(responseMsg);
            } catch (Exception ex) {
                log.info("---error--"+ex.getMessage());
            }
        }
    }

    /**
     * @param userId
     * @param throwable
     * @param session
     */
    @OnError
    public void onError(@PathParam("userId") String userId,
                        Throwable throwable,
                        Session session) {
        log.info("[WebSocketServer] Connection Exception : userId = " + userId + " , throwable = " + throwable.getMessage());
    }

    /**
     * 后台发送消息到redis
     *
     * @param message
     */
    public void sendMessage(String message) {
        log.info("【websocket消息】广播消息:" + message);
        Map baseMap = new HashMap();
        baseMap.put("userId", "");
        baseMap.put("message", message);
        epRedisClient.sendMessage(REDIS_TOPIC_NAME, baseMap);
    }

    /**
     * 此为单点消息
     *
     * @param userId
     * @param message
     */
    public void sendMessage(String userId, String message) {
        Map baseMap = new HashMap();
        baseMap.put("userId", userId);
        baseMap.put("message", message);
        epRedisClient.sendMessage(REDIS_TOPIC_NAME, baseMap);
    }

    /**
     * 此为单点消息(多人)
     *
     * @param userIds
     * @param message
     */
    public void sendMessage(String[] userIds, String message) {
        for (String userIdStr : userIds) {
            sendMessage(userIdStr, message);
        }
    }

    /**
     * 系统（所有在线的）
     */
    public void pushByAll(JSONObject message) throws IOException {
        sendMessage(message.toJSONString());
    }

    /**
     * 单发
     */
    public void pushByOne(WebSocketDto webSocketObject, String userId) throws IOException {
        Session session = sessionPool.get(userId);
        String msgString = JSON.toJSONString(webSocketObject);
        if (session != null && session.isOpen()) {
            try {
                log.debug("【websocket消息】 单点消息userId:" + userId + " message" + msgString);
                synchronized (session) {
                    session.getAsyncRemote().sendText(msgString);
                }
            } catch (Exception ex) {
                log.debug("调用pushMessage异常，消息通道中断，非正常退出！");
            }
        } else if (mqSendFlag) {
            try {
                log.debug("【websocket消息】 单点消息,没有找到userId：" + userId + "会话！通过订阅发布机制，查找其他集群机器发送！");
                sendMessageByMq(webSocketObject);
            } catch (Exception ex) {
                log.debug("【websocket消息】，sendMessageByMq :userId:" + userId + "非正常推送！");
            }
        } else {
            log.debug("【websocket消息】，没有找到userId：" + userId + "session==null？" + (session == null) + ",且未开启mq推送！");
        }
    }

    /**
     * 发送自定义消息
     */
    public void pushInfo(JSONObject message, String userId) throws IOException {
        log.debug("发送消息到:" + userId + "，报文:" + message);
        pushMessage(userId, message.toJSONString());
    }
}