package com.qm.ep.sysres.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "MessageSendNoticeDTO对象", description = "发件箱信息")
@Data
public class MessageSendNoticeDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String vTitle;
    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String vContext;
    /**
     * 消息类型id 依据固定值赋值
     */
    @ApiModelProperty(value = "消息类型id 依据固定值赋值")
    private String vTypeId;
    /**
     * 消息类型id 依据固定值赋值
     */
    @ApiModelProperty(value = "消息类型名字默认为'系统自助消息模板'")
    private String vTypeName;
    /**
     * 我的模板id 依据固定值填写
     */
    @ApiModelProperty(value = "我的模板id 依据固定值填写")
    private String vManagerId;
    /**
     * 发送人员id
     */
    @ApiModelProperty(value = "发送人员id")
    private String sendPersonId;
    /**
     * 发送人员代码
     */
    @ApiModelProperty(value = "发送人员代码")
    private String sendPersonCode;
    /*
     * 发送人员名字
     * */
    @ApiModelProperty(value = "发送人员名字")
    private String sendPersonName;

    /**
     * 发送人原列表
     */
    @ApiModelProperty(value = "发送人原列表")
    private List<ICAPPersonInfoOutDTO> recievePersonList;

}
