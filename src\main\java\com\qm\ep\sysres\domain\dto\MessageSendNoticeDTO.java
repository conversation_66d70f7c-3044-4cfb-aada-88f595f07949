package com.qm.ep.sysres.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.util.List;

@Schema(title = "发件箱信息", description = "发件箱信息")
@Data
public class MessageSendNoticeDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(title = "主键", description = "主键")
    private String id;
    /**
     * 标题
     */
    @Schema(title = "标题", description = "标题")
    private String vTitle;
    /**
     * 消息内容
     */
    @Schema(title = "消息内容", description = "消息内容")
    private String vContext;
    /**
     * 消息类型id 依据固定值赋值
     */
    @Schema(title = "消息类型id 依据固定值赋值", description = "消息类型id 依据固定值赋值")
    private String vTypeId;
    /**
     * 消息类型id 依据固定值赋值
     */
    @Schema(title = "消息类型名字默认为'系统自助消息模板'", description = "消息类型名字默认为'系统自助消息模板'")
    private String vTypeName;
    /**
     * 我的模板id 依据固定值填写
     */
    @Schema(title = "我的模板id 依据固定值填写", description = "我的模板id 依据固定值填写")
    private String vManagerId;
    /**
     * 发送人员id
     */
    @Schema(title = "发送人员id", description = "发送人员id")
    private String sendPersonId;
    /**
     * 发送人员代码
     */
    @Schema(title = "发送人员代码", description = "发送人员代码")
    private String sendPersonCode;
    /*
     * 发送人员名字
     * */
    @Schema(title = "发送人员名字", description = "发送人员名字")
    private String sendPersonName;

    /**
     * 发送人原列表
     */
    @Schema(title = "发送人原列表", description = "发送人原列表")
    private List<ICAPPersonInfoOutDTO> recievePersonList;

}
