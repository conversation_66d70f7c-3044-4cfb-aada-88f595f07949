package com.qm.ep.sysres.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21
 */
@Component
public class TdsServiceLogsRemoteFactory implements FallbackFactory<TdsServiceLogsRemote> {

    @Override
    public TdsServiceLogsRemote create(Throwable throwable) {
        TdsServiceLogsRemoteHystrix hystrix = new TdsServiceLogsRemoteHystrix();
        hystrix.setHystrixEx(throwable);
        return hystrix;
    }
}
