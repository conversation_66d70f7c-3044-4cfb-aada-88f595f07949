package com.qm.ep.logs.domain.dto;

import com.qm.ep.logs.domain.es.GateWayLog;
import com.qm.ep.logs.domain.es.SlowSqlLog;
import com.qm.ep.logs.domain.vo.SearchVO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.tds.api.domain.JsonParamDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class PdfInputDTO extends JsonParamDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 访问时间 开始时间
     */
    private String startTime;
    /**
     * 访问时间 结束时间
     */
    private String endTime;
    /**
     * 接口性能分析
     */
    private List<Map<String, String>> mapListgatewayTimeConsumingAnalysis;
    /**
     * 接口日志
     */
    private List<GateWayLog> gateWayLogListgatewayLogsQuery;
    /**
     * 异常分析
     */
    private List<Map<String, String>> mapListsysErrorTimeConsumingAnalysis;
    /**
     * 搜索帮助性能分析
     */
    private List<Map<String, String>> mapListsearchHelpAnalysis;
    /**
     * 搜索帮助日志
     */
    private List<SearchVO> searchVOListsearchQueryTime;
    /**
     * 慢sql日志
     */
    private List<SlowSqlLog> slowSqlLogList;
    /**
     * 定时任务-异常统计
     */
    private List<QuartzStatisticsVO> quartzStatisticsVOStatusList01;
    /**
     * 定时任务-执行统计
     */
    private List<QuartzStatisticsVO> quartzStatisticsVOStatusList02;

}
