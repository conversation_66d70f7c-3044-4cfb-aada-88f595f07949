package com.qm.ep.sys.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * <p>
 * 获取业务类型对应的附件文件列表信息的入参
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Schema(title = "获取业务类型对应的附件文件列表信息的入参", description = "获取业务类型对应的附件文件列表信息的入参")
@Data
public class AttachImputItemDTO {
    @Schema(title = "哪个业务场景使用，公司+业务类型唯一", description = "哪个业务场景使用，公司+业务类型唯一")
    private String vbustype;
    @Schema(title = "对应单据ID", description = "对应单据ID")
    private String nbillid;
    @Schema(title = "0 是浏览，1是维护", description = "0 是浏览，1是维护")
    private String usemode;
    @Schema(title = "区分是附件(FILE)还是图片(PIC) ", description = "区分是附件(FILE)还是图片(PIC) ")
    private String vmntnmode;
}
