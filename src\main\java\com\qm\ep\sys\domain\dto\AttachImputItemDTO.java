package com.qm.ep.sys.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 获取业务类型对应的附件文件列表信息的入参
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@ApiModel(value = "AttachImputItemDTO对象", description = "获取业务类型对应的附件文件列表信息的入参")
@Data
public class AttachImputItemDTO {
    @ApiModelProperty(value = "哪个业务场景使用，公司+业务类型唯一")
    private String vbustype;
    @ApiModelProperty(value = "对应单据ID")
    private String nbillid;
    @ApiModelProperty(value = "0 是浏览，1是维护")
    private String usemode;
    @ApiModelProperty(value = "区分是附件(FILE)还是图片(PIC) ")
    private String vmntnmode;
}
