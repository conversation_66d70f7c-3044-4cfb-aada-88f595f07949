# JDK17兼容性修复部署指南

## 部署前准备

### 1. 环境检查
```bash
# 确认JDK版本
java -version

# 确认Maven版本
mvn -version

# 检查现有服务状态
ps aux | grep tds-service-sys-res
```

### 2. 备份现有配置
```bash
# 备份当前配置文件
cp src/main/resources/bootstrap.yml src/main/resources/bootstrap.yml.backup
cp src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java.backup
```

## 部署步骤

### 1. 编译和测试
```bash
# 清理并编译
mvn clean compile

# 运行单元测试
mvn test

# 运行特定的JDK17兼容性测试
mvn test -Dtest=HttpClientConfigTest
mvn test -Dtest=HttpClientExceptionHandlerTest
```

### 2. 打包部署
```bash
# 打包应用
mvn clean package -DskipTests

# 检查生成的JAR文件
ls -la target/tds-service-sys-res-*.jar
```

### 3. 配置验证
```bash
# 验证配置文件语法
java -jar target/tds-service-sys-res-*.jar --spring.config.location=src/main/resources/bootstrap.yml --spring.profiles.active=local --logging.level.root=DEBUG --server.port=0 &
sleep 10
kill %1
```

## 部署后验证

### 1. 服务启动验证
```bash
# 启动服务
java -jar target/tds-service-sys-res-*.jar

# 检查启动日志中的HTTP客户端配置
tail -f logs/application.log | grep -i "http"
```

### 2. 健康检查
```bash
# 检查服务健康状态
curl -X GET http://localhost:8339/actuator/health

# 检查HTTP连接池状态（如果有监控端点）
curl -X GET http://localhost:8339/actuator/metrics
```

### 3. 功能测试

#### 基本导出功能测试
```bash
# 测试小数据量导出
curl -X POST http://localhost:8339/table/doExport \
  -H "Content-Type: application/json" \
  -d '{
    "vtranscode": "TEST001",
    "vmenuname": "测试导出",
    "fileName": "test_export.xlsx",
    "serviceName": "/api/test/data",
    "pageSize": 10,
    "sheets": [
      {
        "sheetName": "Sheet1",
        "sheetColumns": [
          {"fieldName": "id", "fieldLabel": "ID", "width": "100"},
          {"fieldName": "name", "fieldLabel": "名称", "width": "200"}
        ]
      }
    ]
  }'
```

#### 大数据量导出测试
```bash
# 测试大数据量导出（模拟原问题场景）
curl -X POST http://localhost:8339/table/doExport \
  -H "Content-Type: application/json" \
  -d '{
    "vtranscode": "INVOICE_QUERY",
    "vmenuname": "终端发票查询",
    "fileName": "终端发票查询.xlsx",
    "serviceName": "/sal-query/searchInvocie/table",
    "pageSize": 800,
    "sheets": [...]
  }'
```

### 4. 性能监控

#### 监控HTTP连接池
```bash
# 监控连接池使用情况
jcmd <PID> VM.classloader_stats | grep -i http

# 监控内存使用
jstat -gc <PID> 5s
```

#### 监控网络连接
```bash
# 监控TCP连接状态
netstat -an | grep :8339
ss -tuln | grep :8339

# 监控HTTP请求
tcpdump -i any -s 0 -A 'port 8339'
```

## 问题排查

### 1. 常见错误及解决方案

#### 错误：`insufficient data written`
**解决方案**：
1. 检查HTTP客户端超时配置
2. 验证目标服务状态
3. 检查网络连接稳定性

```bash
# 检查配置
grep -A 10 "http:" src/main/resources/bootstrap.yml

# 测试网络连通性
telnet <target_host> <target_port>
```

#### 错误：`Connection has been closed`
**解决方案**：
1. 检查Keep-Alive配置
2. 验证连接池设置
3. 检查防火墙规则

```bash
# 检查连接池配置
grep -A 5 "PoolingHttpClientConnectionManager" src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java
```

#### 错误：`The body is not set`
**解决方案**：
1. 检查请求体缓冲设置
2. 验证Content-Type配置
3. 检查Gateway配置

### 2. 日志分析

#### 启用详细日志
```yaml
# 在bootstrap.yml中添加
logging:
  level:
    org.apache.hc: DEBUG
    org.springframework.web.client: DEBUG
    com.qm.ep.sysres: DEBUG
```

#### 关键日志模式
```bash
# 搜索JDK17兼容性相关日志
grep -i "jdk17\|insufficient\|connection.*closed" logs/application.log

# 搜索HTTP客户端异常
grep -i "httpclient\|resourceaccess\|timeout" logs/application.log
```

### 3. 性能调优

#### 根据环境调整配置
```yaml
# 高并发环境
http:
  client:
    max-total: 5000
    default-max-per-route: 200
    response-timeout: 600

# 低延迟环境
http:
  client:
    connect-timeout: 10
    connection-request-timeout: 10
    response-timeout: 120
```

## 回滚方案

### 1. 快速回滚
```bash
# 停止当前服务
kill <PID>

# 恢复备份配置
cp src/main/resources/bootstrap.yml.backup src/main/resources/bootstrap.yml
cp src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java.backup src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java

# 重新编译和启动
mvn clean package -DskipTests
java -jar target/tds-service-sys-res-*.jar
```

### 2. 渐进式回滚
```bash
# 仅回滚HTTP客户端配置
git checkout HEAD~1 -- src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java

# 仅回滚超时配置
sed -i 's/Timeout.ofSeconds(30)/Timeout.ofSeconds(600)/g' src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java
```

## 监控和告警

### 1. 关键指标
- HTTP连接池使用率
- 请求超时频率
- JDK17兼容性异常数量
- 导出功能成功率

### 2. 告警规则
```yaml
# 示例告警配置
alerts:
  - name: "HTTP连接异常"
    condition: "insufficient_data_written_count > 5"
    action: "发送告警邮件"
  
  - name: "导出功能异常"
    condition: "export_failure_rate > 10%"
    action: "发送紧急告警"
```

## 联系信息

如遇到问题，请联系：
- 开发团队：[开发团队邮箱]
- 运维团队：[运维团队邮箱]
- 紧急联系：[紧急联系方式]
