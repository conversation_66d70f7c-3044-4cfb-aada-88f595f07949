package com.qm.ep.sysres.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/1/28$ 15:51$
 **/
@Data
public class WebSocketDto {
    @NotNull(message = "发送消息不可为空")
    @ApiModelProperty("消息体")
    private Map<String, Object> message;
    @ApiModelProperty("要发送的用户")
    private List<String> userIDs;
    @ApiModelProperty("发送类型，1 按照用户发送 2 全部发送")
    private Integer sendType;
    @ApiModelProperty("用户时间戳")
    private String userStamp;
}
