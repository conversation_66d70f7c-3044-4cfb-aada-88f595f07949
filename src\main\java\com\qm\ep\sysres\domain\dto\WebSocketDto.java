package com.qm.ep.sysres.domain.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/1/28$ 15:51$
 **/
@Schema(description = "消息体")
@Data
public class WebSocketDto {
    @Schema(description = "消息体")
    @NotNull(message = "发送消息不可为空")
    private Map<String, Object> message;
    @Schema(description = "要发送的用户")
    private List<String> userIDs;
    @Schema(description = "发送类型，1 按照用户发送 2 全部发送")
    private Integer sendType;
    @Schema(description = "用户时间戳")
    private String userStamp;
}
