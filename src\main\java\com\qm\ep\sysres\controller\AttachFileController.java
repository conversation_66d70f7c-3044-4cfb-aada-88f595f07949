package com.qm.ep.sysres.controller;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qm.ep.logs.domain.dto.*;
import com.qm.ep.logs.domain.es.GateWayLog;
import com.qm.ep.logs.domain.es.SlowSqlLog;
import com.qm.ep.logs.domain.vo.SearchVO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.ep.sysres.domain.bean.AttachFileDO;
import com.qm.ep.sysres.domain.dto.FileInDTO;
import com.qm.ep.sysres.domain.vo.AttachCopyBillVO;
import com.qm.ep.sysres.service.AttachFileService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.service.UploadFileService;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Controller
 * 按业务单据记录对应附件信息JsonResultVo
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Slf4j
@RestController
@RequestMapping("/attachFile")
public class AttachFileController extends BaseController {
    @Autowired
    private AttachFileService attachFileService;
    @Autowired
    private UploadFileService uploadFileService;
    @Autowired
    private I18nUtil i18nUtil;

    @ApiOperation("保存附件数据")
    @PostMapping("/saveAttachInfo")
    public JsonResultVo<AttachFileDO> saveAttachInfo(@RequestBody AttachFileDO attachFileDO) {
        JsonResultVo<AttachFileDO> resultObj = new JsonResultVo<>();
        boolean bRet = attachFileService.save(attachFileDO);
        if (bRet) {
            resultObj.setData(attachFileDO);
            String message = i18nUtil.getMessage("MSG.sysres.common.uploadSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.sysres.common.uploadFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 上传文件
     */
    @DS("master")
    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public JsonResultVo<Object> upload(MultipartHttpServletRequest request) throws IOException {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();
        AttachFileDO attachFileDO = attachFileService.upload(request);
        if (!BootAppUtil.isNullOrEmpty(attachFileDO.getId())) {
            resultObj.setData(attachFileDO);
            String message = i18nUtil.getMessage("MSG.sysres.common.uploadSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.sysres.common.uploadFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 下载Exce导入模板文件
     *
     * @param req Request请求对象
     * @param res Request相应对象
     */
    @ApiOperation("下载Exce导入模板文件")
    @GetMapping({"/downloadExcelTemplate"})
    public void downloadExcelTemplate(HttpServletRequest req, HttpServletResponse res) {
        uploadFileService.download(req, res);
    }

    /**
     * 下载文件
     */
    @ApiOperation("下载文件")
    @GetMapping("/download")
    public void download(HttpServletRequest req, HttpServletResponse res) throws IOException {
        attachFileService.download(req, res);
    }

    /**
     * 复制附件数据
     */
    @ApiOperation("复制附件数据")
    @PostMapping("/copy")
    public JsonResultVo<List<AttachFileDO>> copy(@Valid @RequestBody AttachCopyBillVO copyParam) {
        JsonResultVo<List<AttachFileDO>> result = new JsonResultVo<>();
        List<AttachFileDO> innerResult = attachFileService.copy(copyParam, getUserInfo());
        result.setData(innerResult);
        String message = i18nUtil.getMessage("MSG.sysres.common.copySuccess");
        result.setMsg(message);
        return result;
    }

    @ApiOperation("接口性能分析")
    @PostMapping("/gatewayTimeConsumingAnalysis")
    public JsonResultVo<List<Map<String, String>>> timeConsumingAnalysis(@RequestBody AnalyseDto analyseDto) {
        return attachFileService.timeConsumingAnalysis(analyseDto);
    }

    @ApiOperation("查询网关日志")
    @PostMapping("/gatewayLogsQuery")
    public JsonResultVo<QmPage<GateWayLog>> gatewayLogsQuery(@RequestBody GateWayLogDTO gateWayLogDTO) {
        return attachFileService.gatewayLogsQuery(gateWayLogDTO);
    }

    @ApiOperation("异常分析")
    @PostMapping("/sysErrorTimeConsumingAnalysis")
    public JsonResultVo<List<Map<String, String>>> sysErrorTimeConsumingAnalysis(@RequestBody SysErrorDTO sysErrorDTO) {
        return attachFileService.sysErrorTimeConsumingAnalysis(sysErrorDTO);
    }

    @ApiOperation("接口性能分析")
    @PostMapping("/gatewayLogsSearchHelpAnalysis")
    public JsonResultVo<List<Map<String, String>>> searchHelpAnalysis(@RequestBody SearchHelpAnalyseDto searchHelpAnalyseDto) {
        return attachFileService.searchHelpAnalysis(searchHelpAnalyseDto);
    }

    @ApiOperation("查询网关日志")
    @PostMapping("/gatewayLogsSearchQueryTime")
    public JsonResultVo<QmPage<SearchVO>> searchQueryTime(@RequestBody GateWayLogDTO gateWayLogDTO) {
        return attachFileService.searchQueryTime(gateWayLogDTO);
    }

    @ApiOperation("查询慢sql日志")
    @PostMapping("/slowSqlLogQuery")
    public JsonResultVo<QmPage<SlowSqlLog>> slowSqlLogQuery(@RequestBody SlowSqlLogDTO slowSqlLogDTO) {
        return attachFileService.slowSqlLogQuery(slowSqlLogDTO);
    }

    @ApiOperation(value = "执行统计")
    @PostMapping("/quartzExecutionStatistics")
    public JsonResultVo executionStatistics(@RequestBody QuartzDTO dto) {
        return attachFileService.executionStatistics(dto);
    }

    @ApiOperation(value = "带分页执行统计")
    @PostMapping("/quartzExecutionStatisticsQuery")
    public JsonResultVo<QmPage<QuartzStatisticsVO>> executionStatisticsQuery(@RequestBody QuartzDTO dto) {
        return attachFileService.executionStatisticsQuery(dto);
    }

    @DS("master")
    @ApiOperation("上传文件")
    @PostMapping(value = "/uploadFile")
    public JsonResultVo<Object> uploadFile(MultipartHttpServletRequest request) throws IOException {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();
        AttachFileDO attachFileDO = attachFileService.upload(request);
        if (!BootAppUtil.isNullOrEmpty(attachFileDO.getId())) {
            resultObj.setData(attachFileDO);
            String message = i18nUtil.getMessage("MSG.sysres.common.uploadSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.sysres.common.uploadFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }


    @ApiOperation("下载文件")
    @GetMapping(value = "/downloadFile")
    public void downloadFile(HttpServletRequest req, HttpServletResponse res) throws IOException {
        attachFileService.download(req, res);
    }

    /**
     * 代理制
     * DMS文件走公网传输文件
     * EP从内网获取文件
     *
     * 通过文件id获取文件对象
     * @param inDTO
     * @return
     */
    @ApiOperation("通过文件id获取文件")
    @PostMapping(value = "/getAttachFile", headers = {"tenantId=15"})
    public byte[] getAttachFile(HttpServletRequest request, HttpServletResponse response, @RequestBody FileInDTO inDTO) {
        String id = inDTO.getId();
        log.info("file id =" + id);
        return attachFileService.getAttachFile(request, response, id);
    }

}
