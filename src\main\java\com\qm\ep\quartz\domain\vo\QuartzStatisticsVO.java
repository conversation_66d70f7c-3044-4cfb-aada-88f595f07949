package com.qm.ep.quartz.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(value = "QuartzStatisticsVO", description = "定时任务基本信息")
public class QuartzStatisticsVO implements Serializable {

    private static final long serialVersionUID = 7048335464359170301L;

    /**
     * 任务名
     */
    @ApiModelProperty(value = "任务名")
    private String jobName;

    @ApiModelProperty(value = "次数")
    private String nqty;
}
