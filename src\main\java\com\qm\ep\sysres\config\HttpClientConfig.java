package com.qm.ep.sysres.config;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.DefaultConnectionKeepAliveStrategy;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;

/**
 * HTTP客户端配置类
 * 专门处理JDK17兼容性问题
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Configuration
@ConfigurationProperties(prefix = "http.client")
public class HttpClientConfig {

    // 连接池配置
    private int maxTotal = 2700;
    private int defaultMaxPerRoute = 100;
    
    // 超时配置（秒）
    private int connectTimeout = 30;
    private int connectionRequestTimeout = 30;
    private int responseTimeout = 300;
    
    // 重试配置
    private int retryCount = 3;
    private int retryInterval = 1;
    
    // Keep-Alive配置（分钟）
    private int keepAliveTime = 5;

    /**
     * 创建针对JDK17优化的HTTP客户端工厂
     * 
     * @return ClientHttpRequestFactory
     */
    @Bean("jdk17CompatibleHttpClientFactory")
    public ClientHttpRequestFactory createJdk17CompatibleHttpClientFactory() {
        // 配置连接池
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(maxTotal);
        connectionManager.setDefaultMaxPerRoute(defaultMaxPerRoute);
        
        // JDK17兼容性：设置连接验证策略
        connectionManager.setValidateAfterInactivity(TimeValue.ofSeconds(30));

        // 配置超时 - JDK17兼容性：使用更合理的超时时间
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(Timeout.ofSeconds(connectTimeout))
                .setConnectionRequestTimeout(Timeout.ofSeconds(connectionRequestTimeout))
                .setResponseTimeout(Timeout.ofSeconds(responseTimeout))
                // JDK17兼容性：禁用重定向自动处理
                .setRedirectsEnabled(false)
                .build();

        // 构建 HttpClient - JDK17兼容性配置
        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setRetryStrategy(new DefaultHttpRequestRetryStrategy(retryCount, TimeValue.ofSeconds(retryInterval)))
                // JDK17兼容性：设置Keep-Alive策略
                .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                // JDK17兼容性：设置用户代理
                .setUserAgent("TDS-SysRes-Service/1.0 (JDK17-Compatible)")
                .build();

        // 创建工厂并设置JDK17兼容性选项
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        // JDK17兼容性：设置缓冲请求体，避免流传输问题
        factory.setBufferRequestBody(true);
        
        return factory;
    }

    // Getter和Setter方法
    public int getMaxTotal() {
        return maxTotal;
    }

    public void setMaxTotal(int maxTotal) {
        this.maxTotal = maxTotal;
    }

    public int getDefaultMaxPerRoute() {
        return defaultMaxPerRoute;
    }

    public void setDefaultMaxPerRoute(int defaultMaxPerRoute) {
        this.defaultMaxPerRoute = defaultMaxPerRoute;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getConnectionRequestTimeout() {
        return connectionRequestTimeout;
    }

    public void setConnectionRequestTimeout(int connectionRequestTimeout) {
        this.connectionRequestTimeout = connectionRequestTimeout;
    }

    public int getResponseTimeout() {
        return responseTimeout;
    }

    public void setResponseTimeout(int responseTimeout) {
        this.responseTimeout = responseTimeout;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public int getRetryInterval() {
        return retryInterval;
    }

    public void setRetryInterval(int retryInterval) {
        this.retryInterval = retryInterval;
    }

    public int getKeepAliveTime() {
        return keepAliveTime;
    }

    public void setKeepAliveTime(int keepAliveTime) {
        this.keepAliveTime = keepAliveTime;
    }
}
