# Gateway服务JDK17兼容性配置示例
# 请将此配置合并到Gateway服务的application.yml中

spring:
  cloud:
    gateway:
      # JDK17兼容性：HTTP客户端配置
      httpclient:
        # 连接超时：30秒
        connect-timeout: 30000
        # 响应超时：600秒，支持大数据导出
        response-timeout: 600s
        # 连接池配置
        pool:
          type: elastic
          max-connections: 500
          max-idle-time: 30s
          max-life-time: 5m
          acquire-timeout: 30s
          evict-in-background: 120s
        # JDK17兼容性：启用压缩但限制大小
        compression: true
        # 最大响应头大小
        max-header-size: 16KB
        # 最大初始行长度
        max-initial-line-length: 4KB
        
      # 全局过滤器配置
      default-filters:
        # JDK17兼容性：添加重试机制
        - name: Retry
          args:
            retries: 2
            statuses: BAD_GATEWAY,GATEWAY_TIMEOUT,SERVICE_UNAVAILABLE
            methods: GET,POST
            backoff:
              firstBackoff: 2s
              maxBackoff: 10s
              factor: 2
              basedOnPreviousValue: false
        
        # 请求大小限制
        - name: RequestSize
          args:
            maxSize: 100MB
            
      # 路由配置示例（针对导出服务）
      routes:
        - id: sal-query-route
          uri: lb://sal-query-service
          predicates:
            - Path=/sal-query/**
          filters:
            # 针对导出接口的特殊超时配置
            - name: CircuitBreaker
              args:
                name: sal-query-cb
                fallbackUri: forward:/fallback/sal-query
            # 增加导出接口的超时时间
            - name: Timeout
              args:
                timeout: 600s

# JDK17兼容性：Reactor Netty配置
reactor:
  netty:
    http:
      server:
        # 服务器端配置
        max-request-size: 100MB
        connection-timeout: 30s
        # 空闲超时
        idle-timeout: 300s
        
      client:
        # 客户端连接池配置
        pool:
          max-connections: 500
          max-idle-time: 30s
          max-life-time: 5m
          acquire-timeout: 30s
          evict-in-background: 120s
        # 响应超时
        response-timeout: 600s
        # 连接超时
        connect-timeout: 30s

# JDK17兼容性：WebFlux配置
spring:
  webflux:
    # 最大内存大小
    multipart:
      max-in-memory-size: 10MB
      max-disk-usage-per-part: 100MB
      max-parts: 128
    # 编解码器配置
    codecs:
      max-in-memory-size: 10MB

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    # Gateway相关日志
    org.springframework.cloud.gateway: INFO
    org.springframework.cloud.gateway.filter: DEBUG
    org.springframework.cloud.gateway.route: INFO
    
    # Reactor Netty日志
    reactor.netty: INFO
    reactor.netty.http.client: DEBUG
    reactor.netty.http.server: DEBUG
    
    # 自定义过滤器日志
    com.qm.cloud.gateway.filter: DEBUG
    
    # HTTP客户端日志
    org.springframework.web.reactive.function.client: DEBUG

# 应用特定配置
app:
  gateway:
    # JDK17兼容性配置
    jdk17:
      # 启用JDK17兼容模式
      enabled: true
      # 连接池配置
      connection-pool:
        max-connections: 500
        max-idle-time: 30s
        max-life-time: 5m
      # 超时配置
      timeout:
        connect: 30s
        response: 600s
        read: 600s
        write: 60s
      # 重试配置
      retry:
        max-attempts: 2
        backoff:
          initial: 2s
          max: 10s
          multiplier: 2
    
    # 导出服务特殊配置
    export:
      # 导出服务的超时时间
      timeout: 900s
      # 导出服务的重试次数
      retry-attempts: 1
      # 大文件处理
      large-file:
        threshold: 10MB
        timeout: 1800s

# 熔断器配置
resilience4j:
  circuitbreaker:
    instances:
      sal-query-cb:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 30s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
        
  timelimiter:
    instances:
      sal-query-cb:
        timeoutDuration: 600s
        cancelRunningFuture: true

# 负载均衡配置
spring:
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
      cache:
        enabled: true
        ttl: 30s
      health-check:
        initial-delay: 0s
        interval: 30s
