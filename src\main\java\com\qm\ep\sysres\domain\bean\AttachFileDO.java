package com.qm.ep.sysres.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 按业务单据记录对应附件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysb080")
@Schema(title = "按业务单据记录对应附件信息", description = "按业务单据记录对应附件信息")
public class AttachFileDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "附件ID", description = "附件ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(title = "哪个业务场景使用，公司+业务类型唯一", description = "哪个业务场景使用，公司+业务类型唯一")
    @TableField("VBUSTYPE")
    private String vbustype;

    @Schema(title = "按公司设置，对应公司ID", description = "按公司设置，对应公司ID")
    @TableField("NCO")
    private String nco;

    @Schema(title = "对应单据ID", description = "对应单据ID")
    @TableField("NBILLID")
    private String nbillid;

    @Schema(title = "文件名", description = "文件名")
    @TableField("VFILENAME")
    private String vfilename;

    @Schema(title = "文件的扩展名", description = "文件的扩展名")
    @TableField("VEXTENSION")
    private String vextension;

    @Schema(title = "附件对应的项目代码", description = "附件对应的项目代码")
    @TableField("VITEM")
    private String vitem;

    @Schema(title = "附件备注", description = "附件备注")
    @TableField("VREMARK")
    private String vremark;

    @Schema(title = "上传人员ID", description = "上传人员ID")
    @TableField("NOPR")
    private String nopr;

    @Schema(title = "上传日期", description = "上传日期")
    @TableField("DUP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dup;

    @Schema(title = "附件链接地址", description = "附件链接地址")
    @TableField("VADDR")
    private String vaddr;

    @Schema(title = "文件流化后浏览器识别的类型", description = "文件流化后浏览器识别的类型")
    @TableField("VCONTENTTYPE")
    private String vcontenttype;

    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(title = "第三方存储地址", description = "第三方存储地址")
    @TableField("vitffulfilename")
    private String vitffulfilename;

}
