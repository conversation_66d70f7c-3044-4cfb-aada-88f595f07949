package com.qm.ep.sysres.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 按业务单据记录对应附件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysb080")
@ApiModel(value = "AttachFileDO对象", description = "按业务单据记录对应附件信息")
public class AttachFileDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "附件ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "哪个业务场景使用，公司+业务类型唯一")
    @TableField("VBUSTYPE")
    private String vbustype;

    @ApiModelProperty(value = "按公司设置，对应公司ID")
    @TableField("NCO")
    private String nco;

    @ApiModelProperty(value = "对应单据ID")
    @TableField("NBILLID")
    private String nbillid;

    @ApiModelProperty(value = "文件名")
    @TableField("VFILENAME")
    private String vfilename;

    @ApiModelProperty(value = "文件的扩展名")
    @TableField("VEXTENSION")
    private String vextension;

    @ApiModelProperty(value = "附件对应的项目代码")
    @TableField("VITEM")
    private String vitem;

    @ApiModelProperty(value = "附件备注")
    @TableField("VREMARK")
    private String vremark;

    @ApiModelProperty(value = "上传人员ID")
    @TableField("NOPR")
    private String nopr;

    @ApiModelProperty(value = "上传日期")
    @TableField("DUP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dup;

    @ApiModelProperty(value = "附件链接地址")
    @TableField("VADDR")
    private String vaddr;

    @ApiModelProperty(value = "文件流化后浏览器识别的类型")
    @TableField("VCONTENTTYPE")
    private String vcontenttype;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @ApiModelProperty(value = "第三方存储地址")
    @TableField("vitffulfilename")
    private String vitffulfilename;

}
