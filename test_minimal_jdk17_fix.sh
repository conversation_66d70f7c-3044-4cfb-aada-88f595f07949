#!/bin/bash

# JDK17最小化修复测试脚本
# 基于JDK8分支的简单配置，只添加最小必要的JDK17兼容性修改

echo "=== JDK17最小化修复测试 ==="
echo "开始时间: $(date)"
echo ""

# 配置
SERVICE_URL="http://localhost:8339"
TEST_ENDPOINT="/table/doExport"

# 1. 检查JDK版本
echo "1. 检查JDK版本..."
java -version
echo ""

# 2. 检查服务状态
echo "2. 检查服务状态..."
if curl -s --connect-timeout 5 "$SERVICE_URL/actuator/health" > /dev/null 2>&1; then
    echo "✅ 服务正在运行"
else
    echo "❌ 服务未运行，请先启动服务"
    exit 1
fi
echo ""

# 3. 验证关键配置
echo "3. 验证关键配置..."

# 检查是否使用了简化的配置
if grep -q "setBufferRequestBody(true)" src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java; then
    echo "✅ 找到关键的JDK17兼容性配置：setBufferRequestBody(true)"
else
    echo "❌ 缺少关键的JDK17兼容性配置"
fi

# 检查是否保持了JDK8风格的简单配置
if grep -q "clientHttpRequestFactory.setReadTimeout(600 \* 1000)" src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java; then
    echo "✅ 保持了JDK8风格的简单超时配置"
else
    echo "❌ 超时配置可能过于复杂"
fi

# 检查是否移除了复杂的RequestConfig
if ! grep -q "RequestConfig.custom()" src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java; then
    echo "✅ 移除了复杂的RequestConfig配置"
else
    echo "⚠️  仍然包含复杂的RequestConfig配置"
fi
echo ""

# 4. 测试基本导出功能
echo "4. 测试基本导出功能..."

test_payload='{
    "vtranscode": "MINIMAL_JDK17_TEST",
    "vmenuname": "最小化JDK17修复测试",
    "fileName": "minimal_jdk17_test.xlsx",
    "serviceName": "/test/data",
    "pageSize": 10,
    "sheets": [
        {
            "sheetName": "TestSheet",
            "sheetColumns": [
                {"fieldName": "id", "fieldLabel": "ID", "width": "100", "useDict": false},
                {"fieldName": "name", "fieldLabel": "名称", "width": "200", "useDict": false}
            ]
        }
    ],
    "includeHeader": true
}'

echo "发送导出请求..."
start_time=$(date +%s)

response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
    -X POST "$SERVICE_URL$TEST_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$test_payload" \
    --max-time 120)

end_time=$(date +%s)
duration=$((end_time - start_time))

# 解析响应
response_body=$(echo "$response" | head -n -2)
http_code=$(echo "$response" | tail -n 2 | head -n 1)
time_total=$(echo "$response" | tail -n 1)

echo "HTTP状态码: $http_code"
echo "响应时间: ${time_total}秒"
echo "总耗时: ${duration}秒"

if [ "$http_code" = "200" ]; then
    echo "✅ 基本导出测试通过"
elif [ "$http_code" = "000" ]; then
    echo "❌ 请求超时或连接失败"
    echo "响应内容: $response_body"
else
    echo "⚠️  基本导出测试失败，状态码: $http_code"
    echo "响应内容: $response_body"
fi
echo ""

# 5. 测试实际的终端发票查询导出
echo "5. 测试实际的终端发票查询导出..."

invoice_payload='{
    "vtranscode": "INVOICE_QUERY",
    "vmenuname": "终端发票查询",
    "fileName": "终端发票查询.xlsx",
    "serviceName": "/sal-query/searchInvocie/table",
    "pageSize": 800,
    "sheets": [
        {
            "sheetName": "InvoiceSheet",
            "sheetColumns": [
                {"fieldName": "id", "fieldLabel": "ID", "width": "100", "useDict": false},
                {"fieldName": "invoiceNo", "fieldLabel": "发票号", "width": "150", "useDict": false},
                {"fieldName": "amount", "fieldLabel": "金额", "width": "120", "useDict": false}
            ]
        }
    ],
    "includeHeader": true
}'

echo "发送终端发票查询导出请求（最大等待10分钟）..."
start_time=$(date +%s)

response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
    -X POST "$SERVICE_URL$TEST_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$invoice_payload" \
    --max-time 600)

end_time=$(date +%s)
duration=$((end_time - start_time))

# 解析响应
response_body=$(echo "$response" | head -n -2)
http_code=$(echo "$response" | tail -n 2 | head -n 1)
time_total=$(echo "$response" | tail -n 1)

echo "HTTP状态码: $http_code"
echo "响应时间: ${time_total}秒"
echo "总耗时: ${duration}秒"

if [ "$http_code" = "200" ]; then
    echo "✅ 终端发票查询导出测试通过"
    echo "🎉 JDK17兼容性问题已解决！"
elif [ "$http_code" = "000" ]; then
    echo "❌ 请求超时"
    echo "这可能表明仍需要进一步调整配置"
else
    echo "⚠️  终端发票查询导出测试失败，状态码: $http_code"
    echo "响应内容: $response_body"
fi
echo ""

# 6. 检查日志中的错误
echo "6. 检查最近的错误日志..."
if [ -f "logs/application.log" ]; then
    echo "检查JDK17兼容性相关错误..."
    recent_errors=$(tail -n 50 logs/application.log | grep -i "insufficient.*data\|connection.*closed\|read.*timed.*out" | tail -n 3)
    
    if [ -n "$recent_errors" ]; then
        echo "⚠️  发现相关错误:"
        echo "$recent_errors"
    else
        echo "✅ 未发现JDK17兼容性相关错误"
    fi
else
    echo "ℹ️  未找到日志文件 logs/application.log"
fi
echo ""

# 7. 生成测试报告
echo "7. 生成测试报告..."
cat > minimal_jdk17_fix_report.txt << EOF
JDK17最小化修复测试报告
生成时间: $(date)

修复策略:
基于JDK8分支的简单有效配置，只添加最小必要的JDK17兼容性修改

关键修改:
1. 保持JDK8风格的简单配置方式
2. 只添加一行关键代码：clientHttpRequestFactory.setBufferRequestBody(true)
3. 移除了复杂的RequestConfig和其他高级配置

测试结果:
- 基本导出: HTTP $http_code, 耗时 ${duration}秒
- 终端发票查询: 已测试
- 服务状态: 正常运行

配置对比:
JDK8分支: 简单有效，使用HttpClient 4.x
JDK17分支: 保持简单，使用HttpClient 5.x + setBufferRequestBody(true)

优势:
1. 最小化修改，降低风险
2. 保持与JDK8分支的一致性
3. 易于理解和维护
4. 专门解决JDK17的核心兼容性问题

建议:
1. 如果测试通过，说明最小化修复策略有效
2. 继续监控生产环境表现
3. 避免过度复杂的配置
EOF

echo "✅ 测试报告已生成: minimal_jdk17_fix_report.txt"
echo ""

echo "=== 测试完成 ==="
echo "结束时间: $(date)"
echo ""
echo "总结:"
echo "- 采用最小化修复策略，基于JDK8分支的简单配置"
echo "- 只添加一行关键代码解决JDK17兼容性问题"
echo "- 如果测试通过，说明简单的解决方案往往是最有效的"
