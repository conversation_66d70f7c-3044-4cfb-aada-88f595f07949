<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.sysres.mapper.AttachBusExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <id column="ID" property="id" />
        <result column="VBUSTYPE" property="vbustype" />
        <result column="VEXTENSION" property="vextension" />
        <result column="NCO" property="nco" />
        <result column="DTSTAMP" property="dtstamp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, VBUSTYPE, VEXTENSION, NCO, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.VBUSTYPE,
                a.VEXTENSION,
                a.NCO,
                a.DTSTAMP,
                a.ID
            from sysc080d a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" /> where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=","> #{item} </foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null"> ${k} IS NULL </when>
                        <otherwise> ${k} = #{v} </otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from ( <include refid="QuerySQL" />${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.sysres.domain.bean.AttachBusExtDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
</mapper>
