package com.qm.ep.sysres.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NoticeFactory implements FallbackFactory<NoticeRemote> {
    @Override
    public NoticeRemote create(Throwable throwable) {
        NoticeRemoteHystrix noticeRemoteHystrix = new NoticeRemoteHystrix();
        noticeRemoteHystrix.setHystrixEx(throwable);
        return noticeRemoteHystrix;
    }
}
