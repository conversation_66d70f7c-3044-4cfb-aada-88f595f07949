package com.qm.ep.sysres.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.obs.services.ObsClient;
import com.obs.services.model.ObsObject;
import com.qcloud.cos.model.COSObjectInputStream;
import com.qcloud.cos.utils.IOUtils;
import com.qm.ep.logs.domain.dto.*;
import com.qm.ep.logs.domain.es.GateWayLog;
import com.qm.ep.logs.domain.es.SlowSqlLog;
import com.qm.ep.logs.domain.vo.SearchVO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.ep.sysres.domain.bean.AttachBusExtDO;
import com.qm.ep.sysres.domain.bean.AttachFileDO;
import com.qm.ep.sysres.domain.bean.Sysc080DO;
import com.qm.ep.sysres.domain.dto.AttachFileDTO;
import com.qm.ep.sysres.domain.dto.ICAPPersonInfoOutDTO;
import com.qm.ep.sysres.domain.dto.MessageSendNoticeDTO;
import com.qm.ep.sysres.domain.vo.AttachCenterFileVO;
import com.qm.ep.sysres.domain.vo.AttachCopyBillVO;
import com.qm.ep.sysres.mapper.AttachFileMapper;
import com.qm.ep.sysres.mapper.Sysc080Mapper;
import com.qm.ep.sysres.remote.CommonServiceQuartzRemote;
import com.qm.ep.sysres.remote.EpSysRemote;
import com.qm.ep.sysres.remote.NoticeRemote;
import com.qm.ep.sysres.remote.TdsServiceLogsRemote;
import com.qm.ep.sysres.service.AttachBusExtService;
import com.qm.ep.sysres.service.AttachFileService;
import com.qm.ep.sysres.service.LocalFileOperator;
import com.qm.ep.sysres.utils.VideoUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.base.domain.MultipartFileDecorator;
import com.qm.tds.base.domain.bean.UploadFileDO;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.base.mapper.UploadFileMapper;
import com.qm.tds.base.remote.tdsfile.TDSFileService;
import com.qm.tds.base.remote.tdsfile.TDSFileServiceSoap;
import com.qm.tds.base.service.impl.UploadFileServiceImpl;
import com.qm.tds.util.*;
import com.qm.tds.util.file.CosOperator;
import com.qm.tds.util.file.FileOperator;
import com.qm.tds.util.file.FtpOperator;
import com.qm.tds.util.file.TdsOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 按业务单据记录对应附件信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Slf4j
@Service
@RefreshScope
public class AttachFileServiceImpl extends QmBaseServiceImpl<AttachFileMapper, AttachFileDO> implements AttachFileService {
    @Autowired
    private UploadFileServiceImpl uploadFileService;
    @Autowired
    private LocalFileOperator localFileOperator;

    @Autowired(required = false)
    private Sysc080Mapper sysc080Mapper;
    @Autowired
    private AttachBusExtService attachBusExtService;
    @Autowired
    private TdsServiceLogsRemote tdsServiceLogsRemote;
    @Autowired
    private CommonServiceQuartzRemote commonServiceQuartzRemote;
    @Autowired
    private EpSysRemote epSysRemote;
    @Autowired
    private I18nUtil i18nUtil;

    @Autowired(
            required = false
    )
    private RestTemplate restTemplate;

    @Autowired
    private ImageUtil imageUtil;


    @Resource
    private UploadFileMapper uploadFileMapper;

    @Autowired
    private NoticeRemote noticeRemote;

    @Value("${tupload.localTempPath:NULL}")
    private String tempVideoPath;

    @Value("${video.evt.flag:false}")
    private boolean vetFlag;

    @Value("${video.evt.maxSize:10}")
    private int maxSize;

    @Value("${video.evt.bitRate:800000}")
    private int bitRate;

    @Value("#{'${qm.base.upload.limit-format:jpg,jpeg,png,jfif}'.split(',')}")
    public List<String> imageFormat;

    private String defaultContentType = "application/octet-stream";
    private String responseHeaderText = "Content-Disposition";
    private String responseFilename = "attachment;filename=";
    private String wwwChartType = "ISO8859-1";
    private String localFileName = "filename";
    private static final String BUSINESS_TYPE = "filemanager/shift";
    private static final String FILE_SEPARATOR = "/";

    @Value("${qm.base.upload.size-limit:2048}")
    public int sizeLimit;
    @Value("${qm.base.upload.size-limit-MB:100}")
    public int sizeLimitMB;

    /**
     * 生成消息类型：以下这个为净月环境合法id。不同的环境可能不同，获取方式请联系系统管理员。
     */
    @Value("${ep.sysres.export.msgtypeid:25026514305741d2a5379181dba9dc70}")
    private String logMsgTypeId;

    private TDSFileServiceSoap webService;

    @Value("${qm.base.upload.tdsv2.url}")
    private String fileServiceUrl;

    @Autowired
    private FileOperator fileOperator;

    @Autowired
    private LocalObsOperator localObsOperator;

    @Autowired
    private COSUtils cosUtils;

    @Value("${qm.base.upload.cos-secretId:null}")
    private String AK;
    @Value("${qm.base.upload.cos-secretKey:null}")
    private String SK;
    @Value("${qm.base.upload.cos-regionName:null}")
    private String ENDPOINT;
    @Value("${qm.base.upload.cos-bucket:null}")
    private String BUCKET_NAME;
    @Value("${qm.base.upload.bucket-path:null}")
    private String BUCKET_PATH;
    @Value("${qm.base.download:old}")
    private String downloadType;

    private static ObsClient obsClient;

    /**
     * 后台上传附件
     *
     * @param multipartFile 附件信息
     * @param billId        业务单据ID
     * @param busType       业务类型。区分文件夹
     * @param compress      对图片是否压缩的标识符。1压缩，其他不压缩
     * @return 附件信息
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public AttachFileDO upload(MultipartFile multipartFile, String billId, String busType, String compress) {
        AttachFileDO attachFileDO = new AttachFileDO();
        // 上传附件
        UploadFileDO uploadFileDO = uploadFileService.upload(multipartFile, busType, compress);
        uploadFileDO = uploadFileDO == null ? new UploadFileDO() : uploadFileDO;

        // 更新附件信息
        LoginKeyDO loginKeyDO = BootAppUtil.getLoginKey();
        attachFileDO.setNco(loginKeyDO.getCompanyId());
        attachFileDO.setNopr(loginKeyDO.getOperatorId());
        attachFileDO.setVbustype(busType);
        attachFileDO.setNbillid(billId);
        attachFileDO.setVfilename(uploadFileDO.getVfilename());
        attachFileDO.setVextension(uploadFileDO.getVtype());
        attachFileDO.setVaddr(uploadFileDO.getVaddr());
        attachFileDO.setVcontenttype(uploadFileDO.getVcontenttype());
        saveOrUpdate(attachFileDO);
        return attachFileDO;
    }

    @Override
    public AttachFileDO upload(MultipartHttpServletRequest request) throws IOException {
        AttachFileDO attachFileDO = new AttachFileDO();
        String busType = request.getParameter("busType");
        String billId = request.getParameter("billId");
        String companyId = request.getParameter("companyId");
        String operatorId = request.getParameter("userId");
        String personCode = request.getParameter("personCode");
        String remark = request.getParameter("remark");
        attachFileDO.setVbustype(busType);
        attachFileDO.setNbillid(billId);
        try {

            Sysc080DO obj = sysc080Mapper.selectOneByBusType(busType);
            String vwatermark = ObjectUtil.isNotEmpty(obj) ? obj.getVwatermark() : "";
            List<String> limitFileFormat = selectAttachBusExtBy(busType);
            UploadFileDO uploadFileDO = null;
            if(vetFlag){
                //判断文件是否为视频文件 是否要进行压缩处理
                uploadFileDO = evtUpload(request, vwatermark, limitFileFormat);
            }else{
                uploadFileDO = uploadFileService.upload(request, vwatermark, limitFileFormat);
            }

            if (BootAppUtil.isNullOrEmpty(uploadFileDO)) {
                return attachFileDO;
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVfilename())) {
                attachFileDO.setVfilename(uploadFileDO.getVfilename());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVtype())) {
                attachFileDO.setVextension(uploadFileDO.getVtype());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVaddr())) {
                attachFileDO.setVaddr(uploadFileDO.getVaddr());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVcontenttype())) {
                attachFileDO.setVcontenttype(uploadFileDO.getVcontenttype());
            }
            if (!BootAppUtil.isNullOrEmpty(companyId)) {
                attachFileDO.setNco(companyId);
            }
            if (!BootAppUtil.isNullOrEmpty(remark)) {
                attachFileDO.setVremark(remark);
            }
            if (BootAppUtil.isNullOrEmpty(operatorId) && !BootAppUtil.isNullOrEmpty(personCode)) {
                //operatorId为空，根据personCode获取operatorId
                operatorId = epSysRemote.getPersonIdByCode(personCode).getData();
            }
            if (!BootAppUtil.isNullOrEmpty(operatorId)) {
                attachFileDO.setNopr(operatorId);
            }
            if (!BootAppUtil.isNullOrEmpty(attachFileDO)) {
                saveOrUpdate(attachFileDO);
            }
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
            String message = i18nUtil.getMessage("ERR.sysres.common.uploadFail");
            throw new QmException(message, e);
        }
        return attachFileDO;
    }

    private UploadFileDO evtUpload(MultipartHttpServletRequest request, String sysc080Vwatermark, List<String> limitFileFormat) {
        UploadFileDO uploadFileDO = new UploadFileDO();
        String busType = request.getParameter("busType");
        String businessFolder = request.getParameter("businessType");
        String compress = request.getParameter("compress");
        String watermark = request.getParameter("watermark");
        Iterator fileNameIterator = request.getFileNames();
        boolean sendMsgFlag = false;
        while(fileNameIterator.hasNext()) {
            MultipartFile multipartFile = request.getFile((String)fileNameIterator.next());
            if (null != multipartFile){
                String fileName = multipartFile.getOriginalFilename();
                log.info("压缩视频start-----------{},{},{},{}",fileName,tempVideoPath,bitRate,maxSize);
                if ( fileName.contains("mp4") && VideoUtils.checkFileSize(multipartFile.getSize(),maxSize,"M")) {
                    try {

                        multipartFile = VideoUtils.compressionVideoFile(multipartFile,fileName, tempVideoPath, bitRate);
                        sendMsgFlag = true;
                    } catch (Exception e) {
                        log.info("---error--"+e.getMessage());
                    }
                }
                uploadFileDO = uploadFileService.upload(multipartFile, busType, compress, businessFolder, watermark, sysc080Vwatermark, limitFileFormat);
                if(sendMsgFlag){
                    sendMsg(uploadFileDO.getVaddr(),fileName);
                }
            }
        }

        return uploadFileDO;
    }

    public void sendMsg(String address,String fileName) {
        try {
            JsonResultVo<String> resultVo = noticeRemote.getIdWorkerSTR("EP");
            String id = resultVo.getData();
            MessageSendNoticeDTO messageSendNoticeDTO = new MessageSendNoticeDTO();
            ICAPPersonInfoOutDTO icapPersonInfoOutDTO = new ICAPPersonInfoOutDTO();
            List<ICAPPersonInfoOutDTO> recievePersonList = new ArrayList<>();
            icapPersonInfoOutDTO.setPersonName(BootAppUtil.getLoginKey().getOperatorName());
            icapPersonInfoOutDTO.setId(BootAppUtil.getLoginKey().getOperatorId());
            icapPersonInfoOutDTO.setAccount(BootAppUtil.getLoginKey().getPersonCode());
            recievePersonList.add(icapPersonInfoOutDTO);
            messageSendNoticeDTO.setId(id);
            messageSendNoticeDTO.setVContext(fileName + "压缩完成，并以上传到文件服务器！文件上传路径:"+address);
            messageSendNoticeDTO.setSendPersonCode(BootAppUtil.getLoginKey().getPersonCode());
            messageSendNoticeDTO.setSendPersonId(BootAppUtil.getLoginKey().getOperatorId());
            messageSendNoticeDTO.setSendPersonName(BootAppUtil.getLoginKey().getOperatorName());
            messageSendNoticeDTO.setRecievePersonList(recievePersonList);
            messageSendNoticeDTO.setVTitle(fileName + "视频压缩结果推送！");
            messageSendNoticeDTO.setVTypeId(logMsgTypeId);
            noticeRemote.saveNoticeByOtherUse(messageSendNoticeDTO);
        } catch (Exception e) {
            log.info("---error--"+"发送系统消息失败！" + e.getMessage(), e);
        }
    }

    private List<String> selectAttachBusExtBy(String busType) {
        LambdaQueryWrapper<AttachBusExtDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttachBusExtDO::getVbustype, busType);
        List<AttachBusExtDO> list = attachBusExtService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(AttachBusExtDO::getVextension).collect(Collectors.toList());
    }

    /**
     * 下载文件
     * fileId为文件id
     * downloadFlag 为1时候就是下载模式，没有值或者0为正常打开模式，图片默认打开，文件类型下载。在downloadSuper中生效
     * thumbnailFlag为缩略图标识normal是原图，small是缩略图。在本服务中生效
     * width 缩略图宽。在本服务中生效
     * heigh 缩略图高。在本服务中生效
     */
    @Override
    public boolean download(HttpServletRequest req, HttpServletResponse response) throws IOException {
        String fileId = req.getParameter("fileId");
        if (BootAppUtil.isNullOrEmpty(fileId)) {
            return false;
        }
        AttachFileDTO temp = new AttachFileDTO();
        temp.setId(fileId);
        List<AttachCenterFileVO> attachCenterFileVOList = baseMapper.getAttachCenterFile(temp);
        if (attachCenterFileVOList.isEmpty()) {
            return false;
        }
        //使用闭包方法取值,只取一个值，不要返回list
        AttachCenterFileVO attachCenterFileVO = attachCenterFileVOList.stream()
                .filter(x -> fileId.equals(x.getId())).findAny().orElse(null);
        if (attachCenterFileVO == null) {
            return false;
        }
        UploadFileVO uploadFileVO = new UploadFileVO();
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getId())) {
            uploadFileVO.setId(attachCenterFileVO.getId());
        }
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getVfilename())) {
            uploadFileVO.setVfilename(attachCenterFileVO.getVfilename());
        }
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getVextension())) {
            uploadFileVO.setVtype(attachCenterFileVO.getVextension());
        }
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getVaddr())) {
            uploadFileVO.setVaddr(attachCenterFileVO.getVaddr());
        }
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getVcontenttype())) {
            uploadFileVO.setVcontenttype(attachCenterFileVO.getVcontenttype());
        }
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getVfiletype())) {
            uploadFileVO.setVfiletype(attachCenterFileVO.getVfiletype());
        }
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getVitffulfilename())) {
            uploadFileVO.setVitffulfilename(attachCenterFileVO.getVitffulfilename());
        }
        if (!BootAppUtil.isNullOrEmpty(attachCenterFileVO.getVbustype())) {
            uploadFileVO.setVbustype(attachCenterFileVO.getVbustype());
        }

        if(downloadType.equals("old")){
            log.info("下载模式为旧模式");
            return uploadFileService.downloadSuper(req, response, uploadFileVO);
        }
        if(downloadType.equals("new")){
            log.info("下载模式为新模式");
            return this.downloadSuper(req, response, uploadFileVO);
        }
        throw new QmException("下载模式配置错误，请检查配置项downloadType");
    }

    private boolean downloadSuper(HttpServletRequest req, HttpServletResponse response, UploadFileVO uploadFileVO) {
        if (uploadFileVO == null) {
            return false;
        } else {
            String downloadFlag = req.getParameter("downloadFlag");
            String thumbnailFlag = req.getParameter("thumbnailFlag");
            String widthStr = req.getParameter("width");
            String heightStr = req.getParameter("height");
            response.reset();
            this.responseIntHeader(response, downloadFlag, uploadFileVO);
            int width = 200;
            int heigh = 200;
            if ("small".equals(thumbnailFlag)) {
                width = BootAppUtil.isNullOrEmpty(widthStr) ? width : Integer.parseInt(widthStr);
                heigh = BootAppUtil.isNullOrEmpty(heightStr) ? heigh : Integer.parseInt(heightStr);
                String suffix = this.getSuffixByUploadFileVO(uploadFileVO);
                boolean needCompress = this.imageFormat.contains(suffix.toLowerCase());
                if (("0".equals(downloadFlag) || null == downloadFlag) && !needCompress && !response.getContentType().contains("video")) {
                    byte[] thumbnail = ImageUtil.getThumbnail(suffix, width, heigh);
                    response.setHeader("Accept-Ranges", "");
                    response.setHeader("Content-Length",  String.valueOf(thumbnail.length));
                    response.setContentType("image/jpeg");

                    try {
                        StreamUtils.copy(thumbnail, response.getOutputStream());
                    } catch (IOException var14) {
                        IOException e = var14;
                        log.error("输出文件名缩略图失败！" + e.getMessage(), e);
                    }

                    return true;
                }
            }

            try {
                if (((String)StringUtils.defaultIfBlank(uploadFileVO.getVaddr(), "")).startsWith("http")) {
                    return this.restDownload(response, thumbnailFlag, width, heigh, uploadFileVO);
                } else {
                    this.shiftFile(uploadFileVO);
                    return localObsOperator.downloadFile(response, thumbnailFlag, width, heigh, uploadFileVO);
                }
            } catch (QmException var15) {
                QmException e = var15;
                throw e;
            } catch (Exception var16) {
                Exception e = var16;
                String message = this.i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.downloadError");
                log.error(message, e);
                throw new QmException(message, e);
            }
        }
    }

    private void shiftFile(UploadFileVO uploadFileVO) {
        if (!BootAppUtil.isnotNullOrEmpty(uploadFileVO.getVaddr()) && !BootAppUtil.isNullOrEmpty(uploadFileVO.getVitffulfilename())) {
            String url = uploadFileVO.getVitffulfilename();
            if (this.restTemplate == null) {
                log.info("[-UploadFileServiceImpl-].shiftFile:restTemplate为空");
                this.restTemplate = new RestTemplate();
            }

            String name = uploadFileVO.getVfilename();
            String contentType = uploadFileVO.getVcontenttype();
            String busType = uploadFileVO.getVbustype();
            String basePath = this.getBasePath(busType, "filemanager/shift");

            ResponseEntity fileBytes;
            try {
                fileBytes = this.restTemplate.getForEntity(new URI(url), byte[].class);
            } catch (Exception var15) {
                Exception e = var15;
                log.error("[-UploadFileServiceImpl-].shiftFile:第三方附件下载失败", e);
                return;
            }

            String savename = RandomUtils.getRandomID() + "." + ((String)StringUtils.defaultIfBlank(uploadFileVO.getVtype(), "unkown")).toLowerCase();
            MultipartFile multipartFile = new MultipartFileDecorator((byte[])fileBytes.getBody(), savename, name, contentType);

            try {
                this.uploadFileCenter(basePath, savename, multipartFile, (String)null, (String)null, (String)null);
            } catch (Exception var14) {
                Exception e = var14;
                log.error("[-UploadFileServiceImpl-].shiftFile:第三方附件转存失败", e);
                return;
            }

            UploadFileDO uploadFileDO = new UploadFileDO();
            uploadFileDO.setId(uploadFileVO.getId());
            uploadFileDO.setVaddr(basePath + "/" + savename);

            try {
                boolean b = this.uploadFileMapper.update080(uploadFileDO);
                if (!b) {
                    log.error("[-UploadFileServiceImpl-].shiftFile:本地存储失败={}", uploadFileDO);
                    return;
                }

                uploadFileVO.setVaddr(basePath + "/" + savename);
            } catch (Exception var13) {
                Exception e = var13;
                log.error("[-UploadFileServiceImpl-].shiftFile:本地存储失败", e);
            }

        }
    }

    private boolean restDownload(HttpServletResponse response, String thumbnailFlag, int width, int heigh, UploadFileVO uploadFileVO) throws IOException, URISyntaxException {
        log.info("http开头的URL下载地址为：" + uploadFileVO.getVaddr());
        // ResponseEntity<byte[]> forEntity = this.restTemplate.getForEntity(uploadFileVO.getVaddr(), byte[].class, new Object[0]);
        URI url = new URI(uploadFileVO.getVaddr());
        log.info("下载地址为：" + url);
        ResponseEntity<byte[]> forEntity = this.restTemplate.getForEntity(url, byte[].class);
        byte[] fileBody = (byte[])forEntity.getBody();
        if (ArrayUtils.isEmpty(fileBody)) {
            String message = this.i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.urlWrong");
            throw new QmException(message + "=" + uploadFileVO.getVaddr());
        } else if ("small".equals(thumbnailFlag)) {
            assert fileBody != null;

            ByteArrayInputStream bais = new ByteArrayInputStream(fileBody);
            String suffix = ImageUtil.getSuffix(uploadFileVO.getVfilename());
            InputStream inputStream = this.imageUtil.thumbanailImage(bais, suffix, width, heigh);
            int byteNum = StreamUtils.copy(inputStream, response.getOutputStream());
            return byteNum != 0;
        } else {
            assert fileBody != null;

            StreamUtils.copy(fileBody, response.getOutputStream());
            return true;
        }
    }
    private String getSuffixByUploadFileVO(UploadFileVO uploadFileVO) {
        String suffix = "";
        if (StringUtils.isNotEmpty(uploadFileVO.getVtype())) {
            suffix = uploadFileVO.getVtype();
        } else if (StringUtils.isNotEmpty(uploadFileVO.getVaddr())) {
            String fullPathNameAndSuffix = uploadFileVO.getVaddr();
            String nameAndSuffix = fullPathNameAndSuffix.substring(fullPathNameAndSuffix.lastIndexOf("/") + 1);
            if (nameAndSuffix.lastIndexOf(".") < 0) {
                suffix = "";
            } else {
                suffix = nameAndSuffix.substring(nameAndSuffix.lastIndexOf(".") + 1, nameAndSuffix.length());
            }
        }

        return suffix;
    }

    private void responseIntHeader(HttpServletResponse response, String downloadFlag, UploadFileVO uploadFilevO) {
        String utf8 = StandardCharsets.UTF_8.name();
        String tempDownloadFlag = (String)StringUtils.defaultIfBlank(downloadFlag, "0");
        String contentType = (String)StringUtils.defaultIfBlank(uploadFilevO.getVcontenttype(), this.defaultContentType);
        response.setContentType(contentType);
        if (contentType.contains("video")) {
            response.addHeader("Accept-Ranges", "bytes");
        }

        if ("1".equals(tempDownloadFlag) && !BootAppUtil.isNullOrEmpty(uploadFilevO.getVfilename())) {
            try {
                response.addHeader(this.responseHeaderText, this.responseFilename + new String(uploadFilevO.getVfilename().getBytes(utf8), this.wwwChartType));
                response.addHeader(this.localFileName, URLEncoder.encode(uploadFilevO.getVfilename(), utf8));
            } catch (UnsupportedEncodingException var9) {
                UnsupportedEncodingException e = var9;
                String message = this.i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.responseHeaderException");
                log.error(message, e);
                throw new QmException(message, e);
            }
        }

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<AttachFileDO> copy(AttachCopyBillVO copyParam, LoginKeyDO userInfo) {
        LambdaQueryWrapper<AttachFileDO> queryWrapper = new LambdaQueryWrapper<>();
        String nbillid = copyParam.getOldNbillid();
        String[] vbustypes = copyParam.getVbustypes();
        queryWrapper.eq(AttachFileDO::getNbillid, nbillid);
        // 不全部为空时，进入if条件
        if (!StringUtils.isAllBlank(vbustypes)) {
            queryWrapper.in(AttachFileDO::getVbustype, Arrays.asList(vbustypes));
        }
        // 按 sysb080.nbillid = 入参.单据ID  AND sysb080.vbustype in 入参.业务类型 查询数据；
        List<AttachFileDO> fileList = baseMapper.selectList(queryWrapper);
        return fileList.stream().peek(attachFileDO -> {
            // 清空ID和时间戳，更改nbillid；
            attachFileDO.setId(null);
            attachFileDO.setDtstamp(new Timestamp(System.currentTimeMillis()));
            attachFileDO.setNbillid(copyParam.getNewNbillid());
            attachFileDO.setDup(new Date());
            // 插入一份新数据；
            baseMapper.insert(attachFileDO);
        }).collect(Collectors.toList());
    }

    /**
     * 接口性能分析
     *
     * @param analyseDto
     * @return
     */
    @Override
    public JsonResultVo<List<Map<String, String>>> timeConsumingAnalysis(AnalyseDto analyseDto) {
        return tdsServiceLogsRemote.timeConsumingAnalysis(analyseDto);
    }

    /**
     * 查询网关日志
     *
     * @param gateWayLogDTO
     * @return
     */
    @Override
    public JsonResultVo<QmPage<GateWayLog>> gatewayLogsQuery(GateWayLogDTO gateWayLogDTO) {
        return tdsServiceLogsRemote.save(gateWayLogDTO);
    }

    /**
     * 异常分析
     *
     * @param sysErrorDTO
     * @return
     */
    @Override
    public JsonResultVo<List<Map<String, String>>> sysErrorTimeConsumingAnalysis(SysErrorDTO sysErrorDTO) {
        return tdsServiceLogsRemote.timeConsumingAnalysis(sysErrorDTO);
    }

    @Override
    public JsonResultVo<List<Map<String, String>>> searchHelpAnalysis(SearchHelpAnalyseDto searchHelpAnalyseDto) {
        return tdsServiceLogsRemote.searchHelpAnalysis(searchHelpAnalyseDto);
    }

    @Override
    public JsonResultVo<QmPage<SearchVO>> searchQueryTime(GateWayLogDTO gateWayLogDTO) {
        return tdsServiceLogsRemote.searchQueryTime(gateWayLogDTO);
    }

    @Override
    public JsonResultVo<QmPage<SlowSqlLog>> slowSqlLogQuery(SlowSqlLogDTO slowSqlLogDTO) {
        return tdsServiceLogsRemote.query(slowSqlLogDTO);
    }

    @Override
    public JsonResultVo executionStatistics(QuartzDTO dto) {
        return commonServiceQuartzRemote.executionStatistics(dto);
    }

    @Override
    public JsonResultVo<QmPage<QuartzStatisticsVO>> executionStatisticsQuery(QuartzDTO dto) {
        return commonServiceQuartzRemote.executionStatisticsQuery(dto);
    }

    public byte[] getAttachFile_Bak(HttpServletRequest req, HttpServletResponse response, String fileId) {
        try {
            TDSFileService service = new TDSFileService(new URL(this.fileServiceUrl));
            this.webService = service.getTDSFileServiceSoap();
        } catch (Exception e) {
            throw new QmException("获取webService对象发生异常");
        }

        byte[] fileBody = null;
        AttachFileDO af = this.getById(fileId);
        fileBody = this.webService.getAccessoriesByName(af.getVaddr());
        return fileBody;
    }


    @Override
    public byte[] getAttachFile(HttpServletRequest req, HttpServletResponse response, String fileId) {
        AttachFileDTO temp = new AttachFileDTO();
        temp.setId(fileId);
        List<AttachCenterFileVO> attachCenterFileVOList = baseMapper.getAttachCenterFile(temp);
        //使用闭包方法取值,只取一个值，不要返回list
        AttachCenterFileVO attachCenterFileVO = attachCenterFileVOList.stream()
                .filter(x -> fileId.equals(x.getId())).findAny().orElse(null);

        String vaddr = attachCenterFileVO.getVaddr();

        byte[] b = null;
         if(fileOperator instanceof CosOperator) {
            b = getCosFileByte(vaddr);
        } else if(fileOperator instanceof TdsOperator) {
            b = getTdsFileByte(vaddr);
        } else if (fileOperator instanceof FtpOperator) {
            b = getFtpFileByte(vaddr);
        } else{
            b = getObsFileByte(vaddr);
        }
        return b;
    }

    public byte[] getCosFileByte(String filePath) {
        try {
            COSObjectInputStream cosObjectInputStream = this.cosUtils.downLoadStream(filePath);
            Throwable var3 = null;

            Object var6;
            try {
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                Throwable var5 = null;

                try {
                    StreamUtils.copy(cosObjectInputStream, outputStream);
                    var6 = outputStream.toByteArray();
                } catch (Throwable var31) {
                    var6 = var31;
                    var5 = var31;
                    throw var31;
                } finally {
                    if (outputStream != null) {
                        if (var5 != null) {
                            try {
                                outputStream.close();
                            } catch (Throwable var30) {
                                var5.addSuppressed(var30);
                            }
                        } else {
                            outputStream.close();
                        }
                    }

                }
            } catch (Throwable var33) {
                var3 = var33;
                throw var33;
            } finally {
                if (cosObjectInputStream != null) {
                    if (var3 != null) {
                        try {
                            cosObjectInputStream.close();
                        } catch (Throwable var29) {
                            var3.addSuppressed(var29);
                        }
                    } else {
                        cosObjectInputStream.close();
                    }
                }

            }

            return (byte[])var6;
        } catch (IOException var35) {
            IOException e = var35;
            e.printStackTrace();
            return new byte[0];
        }
    }

    public byte[] getFtpFileByte(String filePath) {
        FtpUtil ftpUtil = new FtpUtil();

        try {
            FTPClient ftpClient = ftpUtil.getFTPClient();
            byte[] b = ftpUtil.getFtpFileByte(ftpClient, filePath);
            return b;
        } catch (IOException var5) {
            IOException e = var5;
            log.info("---error--" + e.getMessage(), e);
            return new byte[0];
        }
    }

    public byte[] getObsFileByte(String filePath) {
        try {
            ObsObject obsObject = this.getObsClient().getObject(this.BUCKET_NAME, this.BUCKET_PATH + "/" + filePath);
            InputStream input = obsObject.getObjectContent();
            return IOUtils.toByteArray(input);
        } catch (IOException var4) {
            IOException e = var4;
            log.info("---error--" + e.getMessage(), e);
            return new byte[0];
        }
    }

    public byte[] getTdsFileByte(String filePath) {
        return this.webService.getAccessoriesByName(filePath);
    }

    public ObsClient getObsClient() {
        if (null == obsClient) {
            obsClient = new ObsClient(this.AK, this.SK, this.ENDPOINT);
        }

        return obsClient;
    }

    private String getBasePath(String busType, String businessFolder) {
        String[] dateSplit = DateFormatUtils.format(new Date(), "yyyy;MMdd").split(";");
        return businessFolder + "/" + (BootAppUtil.isNullOrEmpty(busType) ? "" : busType + "/") + dateSplit[0] + "/" + dateSplit[1];
    }

    private String uploadFileCenter(String basePath, String fileSaveName, MultipartFile mpf, String compress, String watermark, String sysc080Vwatermark) {
        String result = "";

        String suffix;
        try {
            suffix = ImageUtil.getSuffix(mpf.getOriginalFilename());
            boolean needCompress = this.imageFormat.contains(suffix.toLowerCase()) && (StringUtils.isBlank(compress) || "1".equals(compress.trim()));
            long size = mpf.getSize() / 1024L;
            Object mpfDTO;
            if (needCompress && size > 512L) {
                byte[] newFileByte = ImageUtil.compressImg(mpf.getBytes(), this.sizeLimit, suffix);
                mpfDTO = new MultipartFileDecorator(mpf, newFileByte);
            } else {
                mpfDTO = mpf;
            }

            MultipartFile newMpfDTO = this.watermark((MultipartFile)mpfDTO, watermark, sysc080Vwatermark);
            log.debug("开始上传[{}][{}]...", basePath, fileSaveName);
            result = this.fileOperator.uploadFile(basePath, fileSaveName, newMpfDTO);
            log.debug("上传结束[{}][{}][{}]...", new Object[]{basePath, fileSaveName, result});
            return result;
        } catch (Exception var14) {
            Exception e = var14;
            log.error(e.getMessage(), e);
            suffix = this.i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.fileUploadFail");
            throw new QmException(suffix, e);
        }
    }

    private MultipartFile watermark(MultipartFile multipartFile, String watermark, String sysc080Vwatermark) {
        String suffix = ImageUtil.getSuffix(multipartFile.getOriginalFilename());
        boolean images = this.imageFormat.contains(suffix.toLowerCase());
        if (images && StringUtils.isNotEmpty(watermark)) {
            try {
                if (StringUtils.isNotBlank(sysc080Vwatermark)) {
                    Integer num = Integer.valueOf(sysc080Vwatermark);
                    if (num == 1) {
                        return WatermarkFactory.getWatermarkUtils(suffix).bottomRightCorner(multipartFile, watermark);
                    }

                    if (num == 2) {
                        return WatermarkFactory.getWatermarkUtils(suffix).fullScreen(multipartFile, watermark);
                    }
                }
            } catch (IOException | NumberFormatException var7) {
                Exception e = var7;
                log.error(((Exception)e).getMessage(), e);
            }
        }

        return multipartFile;
    }
}


