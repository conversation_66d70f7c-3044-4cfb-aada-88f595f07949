package com.qm.ep.sysres.config;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * JDK17兼容性HTTP客户端配置
 * 专门解决从JDK8升级到JDK17后的HTTP连接问题
 * 
 * 主要解决的问题：
 * 1. insufficient data written
 * 2. Connection has been closed
 * 3. 超时配置不合理
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Configuration
public class Jdk17HttpClientConfig {

    /**
     * 创建JDK17兼容的HTTP客户端工厂
     * 
     * @return ClientHttpRequestFactory
     */
    @Bean
    @Primary
    public ClientHttpRequestFactory jdk17HttpClientFactory() {
        // 1. 配置连接池 - 针对JDK17优化
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(2700);
        connectionManager.setDefaultMaxPerRoute(100);
        // JDK17兼容性：设置连接验证间隔，避免使用过期连接
        connectionManager.setValidateAfterInactivity(TimeValue.ofSeconds(30));

        // 2. 配置超时 - JDK17兼容性：使用更合理的超时时间
        RequestConfig requestConfig = RequestConfig.custom()
                // 连接超时：从600秒减少到30秒，避免长时间等待
                .setConnectTimeout(Timeout.ofSeconds(30))
                // 连接请求超时：从连接池获取连接的超时时间
                .setConnectionRequestTimeout(Timeout.ofSeconds(30))
                // 响应超时：等待服务器响应的时间，保持较长以支持大数据导出
                .setResponseTimeout(Timeout.ofSeconds(300))
                .build();

        // 3. 构建HttpClient - JDK17兼容性配置
        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                // 重试策略：失败时重试3次，间隔1秒
                .setRetryStrategy(new DefaultHttpRequestRetryStrategy(3, TimeValue.ofSeconds(1)))
                // 用户代理：标识客户端
                .setUserAgent("TDS-SysRes-Service/1.0-JDK17")
                .build();

        // 4. 创建Spring的HTTP客户端工厂
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        
        // JDK17兼容性关键配置：启用请求体缓冲
        // 这可以避免"insufficient data written"错误
        factory.setBufferRequestBody(true);
        
        return factory;
    }

    /**
     * 创建使用JDK17兼容配置的RestTemplate
     * 
     * @return RestTemplate
     */
    @Bean
    @Primary
    public RestTemplate jdk17RestTemplate() {
        return new RestTemplate(jdk17HttpClientFactory());
    }
}
