### Send a form with the text and file fields
### Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryP2R5GOLmVqdKkEnb
### POST http://***********/api/sys-res/attachFile/upload
POST {{resurl}}/attachFile/upload
Content-Type: multipart/form-data; boundary=WebAppBoundary
tenantId: 15

--WebAppBoundary
Content-Disposition: form-data; name="compress"
Content-Type: text/plain

1
--WebAppBoundary
Content-Disposition: form-data; name="businessType"
Content-Type: text/plain

szhmd
--WebAppBoundary
Content-Disposition: form-data; name="busType"
Content-Type: text/plain

wework
--WebAppBoundary
Content-Disposition: form-data; name="billId"
Content-Type: text/plain

10000
--WebAppBoundary
Content-Disposition: form-data; name="temp"; filename="0.jpg"
Content-Type: application/json

< C:\takooya\idea_project\qm_server\tds-service-sys-res\src\test\java\com\qm\ep\sysres\http\0.jpg
--WebAppBoundary--

###
#POST http://***********/api/sys-res/attachFile/upload
POST {{resurl}}/attachFile/upload
Content-Type: multipart/form-data; boundary=WebAppBoundary
tenantId: 15
useragent: pc
jwt: {{token}}

--WebAppBoundary
Content-Disposition: form-data; name="compress"
Content-Type: text/plain

1
--WebAppBoundary
Content-Disposition: form-data; name="businessType"
Content-Type: text/plain

business
--WebAppBoundary
Content-Disposition: form-data; name="busType"
Content-Type: text/plain

MDAC337
--WebAppBoundary
Content-Disposition: form-data; name="billId"
Content-Type: text/plain

167152bfdd51ac6edce4799e21d359d3
--WebAppBoundary
Content-Disposition: form-data; name="钉钉首页 - 副本 (2)"; filename="钉钉首页 - 副本 (2).jpg"
Content-Type: application/json

< C:\Users\<USER>\Desktop\启明\Pictures\钉钉首页 - 副本 (2).jpg
--WebAppBoundary--

###
POST {{resurl}}/attachFile/upload
Content-Type: multipart/form-data; boundary=WebAppBoundary
tenantId: 15
useragent: pc
jwt: {{token}}

--WebAppBoundary
Content-Disposition: form-data; name="compress"
Content-Type: text/plain

1
--WebAppBoundary
Content-Disposition: form-data; name="businessType"
Content-Type: text/plain

test
--WebAppBoundary
Content-Disposition: form-data; name="busType"
Content-Type: text/plain

test
--WebAppBoundary
Content-Disposition: form-data; name="4远视储备值1"; filename="4远视储备值1.mp4"
Content-Type: application/json

< C:\Users\<USER>\idea_project\qm_server\tds-service-sys-res\src\test\resources\4远视储备值1.mp4
--WebAppBoundary--
#--WebAppBoundary
#Content-Disposition: form-data; name="billId"
#Content-Type: text/plain
#
#167152bfdd51ac6edce4799e21d359d3
###
GET {{resurl}}/attachFile/download?fileId=538efe74834e247fc5da511dc4de8d0f&thumbnailFlag=normal&tenantId=15&downloadFlag=0&t=wyc0010
jwt: {{token}}
userAgent: pc

###
