package com.qm.ep.sysres.controller;

import com.qm.ep.logs.domain.dto.LogInputDTO;
import com.qm.ep.sysres.domain.vo.AttachFileCenterVO;
import com.qm.ep.sysres.service.ITableService;
import com.qm.ep.sysres.service.PdfFileService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/table")
@Api(value = "表格", tags = {"表格"})
public class TableController {

    @Autowired
    private ITableService tableService;

    @Autowired
    private PdfFileService pdfFileService;

    /**
     * 服务端分页导出功能
     *
     * 
     * @param
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/doExport")
    public void doExport(@RequestBody Map<String, Object> exportInfo, HttpServletResponse response) throws IOException {
        log.info("controller执行doExport");
        Map<String, Object> exportData = tableService.doExport(exportInfo);
        if (exportData.get("fileName") != null) {
            String filename = java.net.URLEncoder.encode(exportData.get("fileName").toString(), "UTF-8");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            byte[] data = exportData.get("data") == null ? null : ((ByteArrayOutputStream) exportData.get("data")).toByteArray();
            if (null != data) {
                log.info("controller执行doExport 调用业务后，存在返回数据data：" + data.length);
                response.setHeader("Content-disposition", "attachment;filename=" + filename);
                OutputStream ouputStream = response.getOutputStream();
                if (exportData.get("data") != null) {
                    ouputStream.write(data);
                    response.setHeader("Content-Length", String.valueOf(data.length));
                }
                ouputStream.flush();
                ouputStream.close();
                log.info("controller执行doExport 执行完成");
            }
        } else if (exportData.get("errMsg") != null) {
            response.sendError(0, exportData.get("errMsg").toString());
        }
    }

    /**
     * 统一日志导出pdf方法
     * ly 20210903
     *
     * @param
     * @param response
     */
    @PostMapping("/upDownExportPDF")
    public JsonResultVo<Integer> upDownExportPDF(HttpServletRequest request, HttpServletResponse response) {
        JsonResultVo<Integer> resultVo = new JsonResultVo<>();
        try {
            Integer nFlage = pdfFileService.upDownExportPDF(request, response);
            resultVo.setData(nFlage);
        } catch (Exception ex) {
            log.info("---error--"+"导出统一日志导出PDF文件Controller层问题，" + ex.getMessage(), ex);
        }
        return resultVo;
    }

    /**
     * 统一日报导出pdf方法
     *
     * @param logInputDTO
     * @return String 文件id信息
     */
    @PostMapping("/getDownExportPdfId")
    public JsonResultVo<AttachFileCenterVO> getDownExportPdfId(@RequestBody LogInputDTO logInputDTO) {
        JsonResultVo<AttachFileCenterVO> resultVo = new JsonResultVo<>();
        AttachFileCenterVO attachFileCenterVO = pdfFileService.getDownExportPdfId(logInputDTO);
        resultVo.setData(attachFileCenterVO);
        return resultVo;
    }
}
