package com.qm.ep.sysres.service;

import com.qm.ep.sysres.domain.dto.WebSocketDto;

import java.io.IOException;
import java.util.List;

public interface WebSocketService {

    /**
     * 发送消息
     *
     * @param webSocketDto
     */
    List<String> sendMessage(WebSocketDto webSocketDto) throws IOException;

    /**
     * 获取在线人数
     *
     * @return
     */
    Long getOnlineNumber();

    /**
     * 更新当前在线人数
     */
    void updateThisOnlineNumber();

    void deleteCloseLink();

    void clearWS();

    void sendMessageByMq(WebSocketDto webSocketDto);
}
