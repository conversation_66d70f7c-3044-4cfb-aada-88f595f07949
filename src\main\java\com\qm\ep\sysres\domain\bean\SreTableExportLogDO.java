package com.qm.ep.sysres.domain.bean;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 导出Excel日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(title = "导出Excel日志", description = "导出Excel日志")
public class SreTableExportLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @Schema(title = "事务码代码", description = "事务码代码")
    private String vtranscode;

    @Schema(title = "用例名称、菜单名称", description = "用例名称、菜单名称")
    private String vmenuname;

    @Schema(title = "公司ID", description = "公司ID")
    private String ncompanyid;

    @Schema(title = "操作员ID", description = "操作员ID")
    private String nopr;

    @Schema(title = "操作员代码", description = "操作员代码")
    private String vopr;

    @Schema(title = "操作员名称", description = "操作员名称")
    private String voprname;

    @Schema(title = "开始时间", description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @Schema(title = "结束时间", description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @Schema(title = "心跳时间。判断后台进程是否已经中断", description = "心跳时间。判断后台进程是否已经中断")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dpulse;

    @Schema(title = "进度", description = "进度")
    private int nprocess;

    @Schema(title = "文件ID", description = "文件ID")
    private String vfileid;

    @Schema(title = "文件大小", description = "文件大小")
    private int nfilesize;

    @Schema(title = "导出参数", description = "导出参数")
    private String vpara;

    @Schema(title = "时间戳", description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(title = "数据总页码", description = "数据总页码")
    private int nrecordpage;
    @Schema(title = "数据记录数", description = "数据记录数")
    private int nrecordsize;
    @Schema(title = "本次动作traceId", description = "本次动作traceId")
    private String vtraceid;


    @Schema(title = "总条数", description = "总条数")
    private int nTotalSize;

    @Schema(title = "每页条数", description = "每页条数")
    private int nPageSize;


    @Schema(title = "导出文件名称", description = "导出文件名称")
    private String vfilename;
}
