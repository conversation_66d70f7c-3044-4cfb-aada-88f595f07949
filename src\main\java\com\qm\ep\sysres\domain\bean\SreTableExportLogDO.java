package com.qm.ep.sysres.domain.bean;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 导出Excel日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SreTableExportLogDO对象", description = "导出Excel日志")
public class SreTableExportLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty(value = "事务码代码")
    private String vtranscode;

    @ApiModelProperty(value = "用例名称、菜单名称")
    private String vmenuname;

    @ApiModelProperty(value = "公司ID")
    private String ncompanyid;

    @ApiModelProperty(value = "操作员ID")
    private String nopr;

    @ApiModelProperty(value = "操作员代码")
    private String vopr;

    @ApiModelProperty(value = "操作员名称")
    private String voprname;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @ApiModelProperty(value = "心跳时间。判断后台进程是否已经中断")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dpulse;

    @ApiModelProperty(value = "进度")
    private int nprocess;

    @ApiModelProperty(value = "文件ID")
    private String vfileid;

    @ApiModelProperty(value = "文件大小")
    private int nfilesize;

    @ApiModelProperty(value = "导出参数")
    private String vpara;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @ApiModelProperty(value = "数据总页码")
    private int nrecordpage;
    @ApiModelProperty(value = "数据记录数")
    private int nrecordsize;
    @ApiModelProperty(value = "本次动作traceId")
    private String vtraceid;


    @ApiModelProperty(value = "总条数")
    private int nTotalSize;

    @ApiModelProperty(value = "每页条数")
    private int nPageSize;


    @ApiModelProperty(value = "导出文件名称")
    private String vfilename;
}
