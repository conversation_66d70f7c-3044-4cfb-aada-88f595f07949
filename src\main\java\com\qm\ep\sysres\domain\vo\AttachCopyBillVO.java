package com.qm.ep.sysres.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 复制单据附件参数
 *
 * <AUTHOR>
 * @since 2021/4/26 9:13
 */
@Schema(description = "复制单据附件参数")
@Data
public class AttachCopyBillVO {
    @Schema(description = "原单据id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "原单据id不可以为空")
    private String oldNbillid;
    @Schema(description = "新单据id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "新单据id不可以为空")
    private String newNbillid;
    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String[] vbustypes;
}
