package com.qm.ep.sysres.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 复制单据附件参数
 *
 * <AUTHOR>
 * @since 2021/4/26 9:13
 */
@Data
@ApiModel(value = "复制单据附件参数", description = "复制单据附件参数")
public class AttachCopyBillVO {
    @ApiModelProperty(value = "原单据id", name = "nbillid", required = true)
    @NotBlank(message = "原单据id不可以为空")
    private String oldNbillid;
    @ApiModelProperty(value = "新单据id", name = "nbillid", required = true)
    @NotBlank(message = "新单据id不可以为空")
    private String newNbillid;
    @ApiModelProperty(value = "业务类型", name = "vbustype", required = false)
    private String[] vbustypes;
}
