package com.qm.ep.sysres.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 按业务类型配置附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysc080")
@Schema(title = "按业务类型配置附件表", description = "按业务类型配置附件表")
public class Sysc080DO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "id", description = "id")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(title = "哪个业务场景使用，公司+业务类型唯一", description = "哪个业务场景使用，公司+业务类型唯一")
    @TableField("VBUSTYPE")
    private String vbustype;

    @Schema(title = "对业务场景说明", description = "对业务场景说明")
    @TableField("VBUSTYPEDESC")
    private String vbustypedesc;

    @Schema(title = "单据ID所在表名", description = "单据ID所在表名")
    @TableField("VTABLE")
    private String vtable;

    @Schema(title = "业务所属模块，SAL、SPA、SVC等", description = "业务所属模块，SAL、SPA、SVC等")
    @TableField("VMODULE")
    private String vmodule;

    @Schema(title = "区分是附件(FILE)还是图片(PIC)", description = "区分是附件(FILE)还是图片(PIC)")
    @TableField("VMNTNMODE")
    private String vmntnmode;

    @Schema(title = "是否对应项目,1对应，0不对应，若对应则在维护界面要选择项目", description = "是否对应项目,1对应，0不对应，若对应则在维护界面要选择项目")
    @TableField("VISITEM")
    private String visitem;

    @Schema(title = "项目对应数据字典代码", description = "项目对应数据字典代码")
    @TableField("VDICTCODE")
    private String vdictcode;

    @Schema(title = "按公司设置，对应公司ID", description = "按公司设置，对应公司ID")
    @TableField("NCO")
    private String nco;

    @Schema(title = "图片加水印模式", description = "图片加水印模式")
    @TableField("VWATERMARK")
    private String vwatermark;

    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;


}
