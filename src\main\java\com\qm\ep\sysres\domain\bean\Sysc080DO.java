package com.qm.ep.sysres.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 按业务类型配置附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysc080")
@ApiModel(value="sysc080对象", description="按业务类型配置附件表")
public class Sysc080DO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "哪个业务场景使用，公司+业务类型唯一")
    @TableField("VBUSTYPE")
    private String vbustype;

    @ApiModelProperty(value = "对业务场景说明")
    @TableField("VBUSTYPEDESC")
    private String vbustypedesc;

    @ApiModelProperty(value = "单据ID所在表名")
    @TableField("VTABLE")
    private String vtable;

    @ApiModelProperty(value = "业务所属模块，SAL、SPA、SVC等")
    @TableField("VMODULE")
    private String vmodule;

    @ApiModelProperty(value = "区分是附件(FILE)还是图片(PIC)")
    @TableField("VMNTNMODE")
    private String vmntnmode;

    @ApiModelProperty(value = "是否对应项目,1对应，0不对应，若对应则在维护界面要选择项目")
    @TableField("VISITEM")
    private String visitem;

    @ApiModelProperty(value = "项目对应数据字典代码")
    @TableField("VDICTCODE")
    private String vdictcode;

    @ApiModelProperty(value = "按公司设置，对应公司ID")
    @TableField("NCO")
    private String nco;

    @ApiModelProperty(value = "图片加水印模式")
    @TableField("VWATERMARK")
    private String vwatermark;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;


}
