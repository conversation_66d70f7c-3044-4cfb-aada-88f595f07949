package com.qm.ep.sysres.utils;

import com.qm.tds.api.exception.QmException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.ResourceAccessException;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HTTP客户端异常处理器测试
 * 验证JDK17兼容性异常处理
 */
class HttpClientExceptionHandlerTest {

    private HttpClientExceptionHandler handler;
    private final String testUrl = "http://test.example.com/api";
    private final int testPageIndex = 1;

    @BeforeEach
    void setUp() {
        handler = new HttpClientExceptionHandler();
    }

    @Test
    void testHandleSocketTimeoutException() {
        SocketTimeoutException timeoutException = new SocketTimeoutException("Read timed out");
        ResourceAccessException resourceException = new ResourceAccessException("Request timeout", timeoutException);
        
        String result = handler.handleHttpException(resourceException, testUrl, testPageIndex);
        
        assertTrue(result.contains("请求超时"));
        assertTrue(result.contains(testUrl));
        assertTrue(result.contains(String.valueOf(testPageIndex)));
    }

    @Test
    void testHandleConnectException() {
        ConnectException connectException = new ConnectException("Connection refused");
        ResourceAccessException resourceException = new ResourceAccessException("Connection failed", connectException);
        
        String result = handler.handleHttpException(resourceException, testUrl, testPageIndex);
        
        assertTrue(result.contains("连接失败"));
        assertTrue(result.contains(testUrl));
    }

    @Test
    void testHandleJdk17CompatibilityIssue() {
        IOException ioException = new IOException("insufficient data written");
        ResourceAccessException resourceException = new ResourceAccessException("I/O error", ioException);
        
        String result = handler.handleHttpException(resourceException, testUrl, testPageIndex);
        
        assertTrue(result.contains("数据传输不完整"));
        assertTrue(result.contains("JDK17兼容性问题"));
    }

    @Test
    void testIsJdk17CompatibilityIssue() {
        // 测试JDK17兼容性问题识别
        IOException ioException1 = new IOException("insufficient data written");
        ResourceAccessException resourceException1 = new ResourceAccessException("I/O error", ioException1);
        assertTrue(handler.isJdk17CompatibilityIssue(resourceException1));
        
        IOException ioException2 = new IOException("Connection has been closed");
        ResourceAccessException resourceException2 = new ResourceAccessException("I/O error", ioException2);
        assertTrue(handler.isJdk17CompatibilityIssue(resourceException2));
        
        // 测试非JDK17兼容性问题
        IOException ioException3 = new IOException("Other IO error");
        ResourceAccessException resourceException3 = new ResourceAccessException("I/O error", ioException3);
        assertFalse(handler.isJdk17CompatibilityIssue(resourceException3));
    }

    @Test
    void testCreateJdk17CompatibilityException() {
        IOException ioException = new IOException("insufficient data written");
        ResourceAccessException resourceException = new ResourceAccessException("I/O error", ioException);
        
        QmException result = handler.createJdk17CompatibilityException(resourceException, testUrl, testPageIndex);
        
        assertNotNull(result);
        assertTrue(result.getMessage().contains("JDK17兼容性问题"));
        assertEquals(resourceException, result.getCause());
    }

    @Test
    void testGetRetryAdvice() {
        // 测试JDK17兼容性问题的重试建议
        IOException ioException = new IOException("insufficient data written");
        ResourceAccessException resourceException = new ResourceAccessException("I/O error", ioException);
        
        String advice = handler.getRetryAdvice(resourceException);
        assertTrue(advice.contains("检查HTTP客户端超时配置"));
        
        // 测试超时异常的重试建议
        SocketTimeoutException timeoutException = new SocketTimeoutException("Read timed out");
        ResourceAccessException timeoutResourceException = new ResourceAccessException("Request timeout", timeoutException);
        
        String timeoutAdvice = handler.getRetryAdvice(timeoutResourceException);
        assertTrue(timeoutAdvice.contains("增加超时时间"));
        
        // 测试连接异常的重试建议
        ConnectException connectException = new ConnectException("Connection refused");
        ResourceAccessException connectResourceException = new ResourceAccessException("Connection failed", connectException);
        
        String connectAdvice = handler.getRetryAdvice(connectResourceException);
        assertTrue(connectAdvice.contains("检查目标服务是否运行"));
    }
}
