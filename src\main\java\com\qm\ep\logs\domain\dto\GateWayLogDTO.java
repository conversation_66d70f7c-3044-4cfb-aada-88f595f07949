package com.qm.ep.logs.domain.dto;


import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: es 日志实体
 * @author: Cyl
 * @time: 2020/07/29 12:55
 */
@Schema(description = "日志实体")
@Data
@SuppressWarnings("squid:S1068")
public class GateWayLogDTO extends JsonParamDto implements Serializable {

    private static final long serialVersionUID = 2073684414842102685L;
    /**
     * id
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 端口
     */
    @Schema(description = "端口")
    private Integer port;

    /**
     * ip
     */
    @Schema(description = "数据ip")
    @JsonAlias("IP")
    private String ip;


    /**
     * 接口地址
     */
    @Schema(description = "接口地址")
    @JsonAlias("URI")
    private String uri;

    /**
     * 入参
     */
    @Schema(description = "入参")
    @JsonAlias("ENTRY_PARAMETERS")
    private String inParams;

    /**
     * 出参
     */
    @Schema(description = "出参")
    @JsonAlias("OUT_PARAMETERS")
    private String outParams;

    /**
     * 消耗时间
     */
    @Schema(description = "消耗时间")
    @JsonAlias("TIME_CONSUMING")
    private String timeConsuming;

    /**
     * 本机地址
     */
    @Schema(description = "本机地址")
    private String host;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @JsonAlias("STATUS")
    private Integer status;

    /**
     * 日志级别
     */
    @Schema(description = "日志级别")
    private String level;

    /**
     * traceId
     */
    @Schema(description = "数据traceId")
    private String traceId;

    /**
     * spanId
     */
    @Schema(description = "数据spanId")
    private String spanId;

    /**
     * 请求类型
     */
    @Schema(description = "请求类型")
    @JsonAlias("HTTP_METHOD")
    private String httpMethod;

    /**
     * 请求服务名称
     */
    @Schema(description = "请求服务名称")
    @JsonAlias("SERVICE_NAME")
    private String serviceName;


    /**
     * 自定义名称
     */
    @Schema(description = "自定义名称")
    @JsonAlias("LOG_SERVER_NAME")
    private String logServerName;

    /**
     * 请求类型
     */
    @Schema(description = "请求类型")
    @JsonAlias("REQUEST_CONTENT_TYPE")
    private String requestContentType;

    /**
     * 响应类型
     */
    @Schema(description = "响应类型")
    @JsonAlias("RESPONSE_CONTENT_TYPE")
    private String responseContentType;

    /**
     * 访问时间
     */
    @Schema(description = "访问时间")
    @JsonAlias("ACCESS_TIME")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accessTime;

    /**
     * request header信息
     */
    @Schema(description = "数据request header信息")
    @JsonAlias("LOGIN_KEY")
    private String loginkey;


    /**
     * 访问时间 开始时间
     */
    @Schema(description = "访问时间 开始时间")
    private String startTime;

    /**
     * 访问时间 结束时间
     */
    @Schema(description = "访问时间 结束时间")
    private String endTime;
    /**
     * 访问时间 开始日期
     */
    @Schema(description = "访问时间 开始日期")
    private String startDate;
    /**
     * 人员代码
     */
    @Schema(description = "人员代码")
    private String personCode;

    /**
     * 公司ID
     */
    @Schema(description = "公司ID")
    private String companyId;
    /**
     * 搜索帮助名称
     */
    @Schema(description = "搜索帮助名称")
    private String name;
    /**
     * 时间索引
     */
    @Schema(description = "时间索引")
    private List<String> searchTimeList;
}
