package com.qm.ep.logs.domain.dto;


import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: es 日志实体
 * @author: Cyl
 * @time: 2020/07/29 12:55
 */
@Data
@SuppressWarnings("squid:S1068")
public class GateWayLogDTO extends JsonParamDto implements Serializable {

    private static final long serialVersionUID = 2073684414842102685L;
    /**
     * id
     */
    private String id;

    /**
     * 端口
     */
    private Integer port;

    /**
     * ip
     */
    @JsonAlias("IP")
    private String ip;


    /**
     * 接口地址
     */
    @JsonAlias("URI")
    private String uri;

    /**
     * 入参
     */
    @JsonAlias("ENTRY_PARAMETERS")
    private String inParams;

    /**
     * 出参
     */
    @JsonAlias("OUT_PARAMETERS")
    private String outParams;

    /**
     * 消耗时间
     */
    @JsonAlias("TIME_CONSUMING")
    private String timeConsuming;

    /**
     * 本机地址
     */
    private String host;

    /**
     * 状态
     */
    @JsonAlias("STATUS")
    private Integer status;

    /**
     * 日志级别
     */
    private String level;

    /**
     * traceId
     */
    private String traceId;

    /**
     * spanId
     */
    private String spanId;

    /**
     * 请求类型
     */
    @JsonAlias("HTTP_METHOD")
    private String httpMethod;

    /**
     * 请求服务名称
     */
    @JsonAlias("SERVICE_NAME")
    private String serviceName;


    /**
     * 自定义名称
     */
    @JsonAlias("LOG_SERVER_NAME")
    private String logServerName;

    /**
     * 请求类型
     */
    @JsonAlias("REQUEST_CONTENT_TYPE")
    private String requestContentType;

    /**
     * 响应类型
     */
    @JsonAlias("RESPONSE_CONTENT_TYPE")
    private String responseContentType;

    /**
     * 访问时间
     */
    @JsonAlias("ACCESS_TIME")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accessTime;

    /**
     * request header信息
     */
    @JsonAlias("LOGIN_KEY")
    private String loginkey;


    /**
     * 访问时间 开始时间
     */
    private String startTime;

    /**
     * 访问时间 结束时间
     */
    private String endTime;
    /**
     * 访问时间 开始日期
     */
    private String startDate;
    /**
     * 人员代码
     */
    private String personCode;

    /**
     * 公司ID
     */
    private String companyId;
    /**
     * 搜索帮助名称
     */
    private String name;
    /**
     * 时间索引
     */
    private List<String> searchTimeList;
}
