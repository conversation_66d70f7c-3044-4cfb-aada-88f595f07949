package com.qm.ep.quartz.domain.dto;

import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-09
 */
@Data
@ApiModel(value = "QuartzDTO", description = "定时任务入参")
public class QuartzDTO extends JsonParamDto implements Serializable {


    private static final long serialVersionUID = 4472346101200205229L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * cron表达式
     */
    @ApiModelProperty(value = "cron表达式")
    private String cronExpression;

    /**
     * 任务调用的方法名
     */
    @ApiModelProperty(value = "任务调用的方法名")
    private String methodName;

    /**
     * 任务是否有状态
     */
    @ApiModelProperty(value = "任务是否有状态")
    private String isConcurrent;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    private String description;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updateBy;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createByName;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "修改者")
    private String updateByName;
    /**
     * 任务执行时调用哪个类的方法 包名+类名
     */
    @ApiModelProperty(value = "任务执行时调用哪个类的方法 包名+类名")
    private String beanClass;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private String jobStatus;

    /**
     * 任务分组
     */
    @ApiModelProperty(value = "任务分组")
    private String jobGroup;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * Spring bean
     */
    @ApiModelProperty(value = "Spring bean")
    private String springBean;

    /**
     * 任务名
     */
    @ApiModelProperty(value = "任务名")
    private String jobName;

    @ApiModelProperty(value = "重叠执行")
    private String isParallel;

    @ApiModelProperty(value = "任务参数")
    private String jobPara;

    /**
     * 时间戳
     */
    @Version
    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @SuppressWarnings("squid:S1948")
    @ApiModelProperty(value = "状态数组")
    private  List statusList;
}
