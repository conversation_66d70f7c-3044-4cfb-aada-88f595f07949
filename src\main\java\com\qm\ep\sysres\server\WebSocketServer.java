package com.qm.ep.sysres.server;

import com.alibaba.fastjson.JSONObject;
import com.qm.ep.sysres.constant.SocketConstant;
import com.qm.tds.dynamic.utils.SpringContextUtils;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
//@ServerEndpoint("/websocket/{userId}")
@Component
@Slf4j
public class WebSocketServer {

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static int onlineCount = 0;
    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
     */
    private static final ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    /**
     * 接收userId
     */
    private String userId = "";

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) throws IOException {
        this.session = session;
        this.userId = userId;
        if (webSocketMap.isEmpty() && !webSocketMap.containsKey(userId)) {
            webSocketMap.put(userId, this);
            //加入set中
            addOnlineCount();
            updateThisOnlineNumber();
        }
        //在线数加1
        log.info("用户连接:" + userId + ",当前在线人数为:" + getOnlineCount());
    }


    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() throws IOException {
        if (webSocketMap.containsKey(userId)) {
            webSocketMap.get(userId).session.close();
            webSocketMap.remove(userId);
            //从set中删除
            subOnlineCount();
            updateThisOnlineNumber();
        }
        log.info("用户退出:" + userId + ",当前在线人数为:" + getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("用户消息:" + userId + ",报文:" + message);
        //可以群发消息
        //消息保存到数据库、redis
    }

    /**
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.info("---error--"+"用户错误:" + userId + ",原因:" + error.getMessage(), error);
        webSocketMap.remove(userId);
    }

    /**
     * 实现服务器主动推送
     */
    public void pushMessage(JSONObject message) throws IOException {
        session.getAsyncRemote().sendText(message.toJSONString());
    }

    /**
     * 发送自定义消息
     */
    public static void pushInfo(JSONObject message, String userId) throws IOException {
        log.debug("发送消息到:" + userId + "，报文:" + message);
        final String user = userId;
        if (StringUtils.isNotBlank(user)) {
            if (webSocketMap.containsKey(user)) {
                webSocketMap.get(user).pushMessage(message);
            }
        } else {
            log.info("---error--"+"用户" + userId + ",不在线！");
        }
    }

    /**
     * Map集合模糊匹配
     *
     * @param map     map集合
     * @param keyLike 模糊key
     * @return
     */
    public static List<WebSocketServer> getLikeByMap(Map<String, WebSocketServer> map, String keyLike) {
        List<WebSocketServer> list = new ArrayList<>();
        for (Map.Entry<String, WebSocketServer> entity : map.entrySet()) {
            if (entity.getKey().contains(keyLike)) {
                list.add(entity.getValue());
            }
        }
        return list;
    }

    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    public synchronized void addOnlineCount() {
        redisServiceInit();
        String redisKey = redisUtils.keyBuilder(SocketConstant.REDIS_KEY_PRE, SocketConstant.SOCKET_USER, userId);
        redisUtils.set(redisKey, userId);
        WebSocketServer.onlineCount++;
    }

    public synchronized void subOnlineCount() {
        redisServiceInit();
        String redisKey = redisUtils.keyBuilder(SocketConstant.REDIS_KEY_PRE, SocketConstant.SOCKET_USER, userId);
        redisUtils.del(redisKey);
        if (WebSocketServer.onlineCount > 0) {
            WebSocketServer.onlineCount--;
        }
    }

    public void clearWS() {
        try {
            webSocketMap.clear();
            log.info("清除REDIS内所有用户....");
            String redisKey = redisUtils.keyBuilder("Socket", SocketConstant.SOCKET_USER, "*");
            redisUtils.del(redisUtils.getKeys(redisKey));
        } catch (Exception e) {
            log.info("---error--"+e.getMessage());
        }
    }

    /**
     * 系统（所有在线的）
     */
    public static void pushByAll(JSONObject message) throws IOException {
        for (Map.Entry<String, WebSocketServer> item : webSocketMap.entrySet()) {
            webSocketMap.get(item.getKey()).pushMessage(message);
        }
    }

    /**
     * 单发
     */
    public static void pushByOne(JSONObject message, String userId) throws IOException {
        pushInfo(message, userId);
    }

    /**
     * 更新在线人数
     */
    public void updateThisOnlineNumber() {
        log.debug("更新在线人数:{}", onlineCount);
        redisServiceInit();
        String redisKey = redisUtils.keyBuilder(SocketConstant.REDIS_KEY_PRE, SocketConstant.SOCKET_ONLINE_NUMBER);
        redisUtils.set(redisKey, onlineCount, 60);
    }

    /**
     * 初始化 redis 服务
     */
    void redisServiceInit() {
        if (redisUtils == null) {
            redisUtils = SpringContextUtils.getBean(RedisUtils.class);
        }
    }

    /**
     * 清除失活链接
     */
    public void deleteCloseLink() {
        log.debug("检查失活链接:{}", webSocketMap.size());
        webSocketMap.forEach((k, v) -> {
            try {
                v.session.getAsyncRemote().sendText("");
            } catch (Exception e) {
                webSocketMap.remove(k);
                log.info("---error--"+e.getMessage(), e);
            }
        });
    }
}
