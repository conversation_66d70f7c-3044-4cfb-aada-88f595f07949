package com.qm.ep.sysres;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.DefaultConnectionKeepAliveStrategy;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.support.AllEncompassingFormHttpMessageConverter;
import org.springframework.http.converter.xml.SourceHttpMessageConverter;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

@SpringBootApplication(scanBasePackages = "com.qm")
@EnableFeignClients(basePackages = {"com.qm"})
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan({"com.qm.tds.dynamic.mapper", "com.qm.tds.base.mapper", "com.qm.ep.sysres.mapper"})
@ComponentScan(basePackages = {"com.qm"})
public class EpSysResServiceIscApplication {

    public static void main(String[] args) {
        SpringApplication.run(EpSysResServiceIscApplication.class, args);
    }

    @Bean
        //@LoadBalanced
    RestTemplate initRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory());

        restTemplate.getMessageConverters().add(new ByteArrayHttpMessageConverter());
        restTemplate.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName("UTF-8")));
        restTemplate.getMessageConverters().add(new ResourceHttpMessageConverter());
        restTemplate.getMessageConverters().add(new SourceHttpMessageConverter<>());
        restTemplate.getMessageConverters().add(new AllEncompassingFormHttpMessageConverter());
        restTemplate.getMessageConverters().add(geFastJsonHttpMessageConverter());
        return restTemplate;
    }

    @Bean
    public static ClientHttpRequestFactory clientHttpRequestFactory() {
        // 配置连接池
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(2700);
        connectionManager.setDefaultMaxPerRoute(100);
        // JDK17兼容性：设置连接验证间隔
        connectionManager.setValidateAfterInactivity(TimeValue.ofSeconds(30));

        // 配置超时（单位：秒）- JDK17兼容性：平衡稳定性和功能需求
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(Timeout.ofSeconds(30))  // 连接超时30秒，快速建立连接
                .setConnectionRequestTimeout(Timeout.ofSeconds(30))  // 连接请求超时30秒
                .setResponseTimeout(Timeout.ofSeconds(600))  // 响应超时600秒，支持大数据导出
                // JDK17兼容性：启用期望继续，改善与客户端的兼容性
                .setExpectContinueEnabled(true)
                .build();

        // 构建 HttpClient - JDK17兼容性：添加更多配置
        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setRetryStrategy(new DefaultHttpRequestRetryStrategy(3, TimeValue.ofSeconds(1)))
                // JDK17兼容性：设置Keep-Alive策略
                .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                // JDK17兼容性：设置用户代理
                .setUserAgent("TDS-SysRes-Service/1.0")
                .build();

        // 使用 HttpClient5 的工厂类
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        // JDK17兼容性：设置缓冲请求体，避免流传输问题
        factory.setBufferRequestBody(true);
        return factory;

//        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
//        // 开始设置连接池
//        PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager();
//        poolingHttpClientConnectionManager.setMaxTotal(2700); // 最大连接数2700
//        poolingHttpClientConnectionManager.setDefaultMaxPerRoute(100); // 同路由并发数100
//        httpClientBuilder.setConnectionManager(poolingHttpClientConnectionManager);
//        httpClientBuilder.setRetryHandler(new DefaultHttpRequestRetryHandler(3, true)); // 重试次数
//        HttpClient httpClient = httpClientBuilder.build();
//        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(
//                httpClient); // httpClient连接配置
//        clientHttpRequestFactory.setConnectionRequestTimeout(600 * 1000);// 连接不够用的等待时间
//        clientHttpRequestFactory.setConnectTimeout(600 * 1000); // 连接超时时间
//        clientHttpRequestFactory.setReadTimeout(600 * 1000); // 读超时时间
//        return clientHttpRequestFactory;
    }

    public static FastJsonHttpMessageConverter geFastJsonHttpMessageConverter() {
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();

        // 升级最新版本需加=============================================================
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        fastConverter.setSupportedMediaTypes(supportedMediaTypes);

        // 创建配置类
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        // 修改配置返回内容的过滤
        fastJsonConfig.setSerializerFeatures(SerializerFeature.DisableCircularReferenceDetect,
                SerializerFeature.WriteMapNullValue);
        fastConverter.setFastJsonConfig(fastJsonConfig);
        return fastConverter;
    }


}
