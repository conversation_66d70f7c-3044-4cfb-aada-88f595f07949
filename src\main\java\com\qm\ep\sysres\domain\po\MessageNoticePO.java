package com.qm.ep.sysres.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 站内信
 *
 * <AUTHOR>
 * @Date 2020-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dlm_message_notice")
@ApiModel(value = "MessageNoticePO对象", description = "发件箱")
public class MessageNoticePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 标题
     */
    @TableField("v_title")
    private String vTitle;

    /**
     * 消息类型
     */
    @TableField("v_type_id")
    private String vTypeId;

    /**
     * 消息内容
     */
    @TableField("v_context")
    private String vContext;

    /**
     * 消息编码
     */
    @TableField("v_serial_number")
    private String serialNumber;

    @TableField("vFactory_title")
    private String vFactoryTitle;

    @TableField("vFactory_context")
    private String vFactoryContext;

    @TableField("vDealer_title")
    private String vDealerTitle;

    @TableField("vDealer_context")
    private String vDealerContext;
    /**
     * 消息编码发送消息后返回的唯一编码，便于查找msg_message表使用,厂端
     */
    @TableField("vFBatchID")
    private String vFBatchID;
    /**
     * 消息编码发送消息后返回的唯一编码，便于查找msg_message表使用，店端
     */
    @TableField("vDBatchID")
    private String vDBatchID;

    /**
     * 销售邮箱发送标识:0不发送，1发送
     */
    @TableField("sales_mail_check")
    private String salesMailCheck;

    /**
     * 服务邮箱发送标识:0不发送，1发送
     */
    @TableField("service_mail_check")
    private String serviceMailCheck;

    /**
     * 是否是全网经销商:1是，0不是
     */
    @TableField("is_all_dealer")
    private String isAllDealer;

    /**
     * 是不是全部人员:1是，0不是
     */
    @TableField("is_all_factory")
    private String isAllFactory;

    @TableField("v_manager_id")
    private String vManagerid;

    /**
     * 消息发送类型（1邮件，2系统通知，3短信）
     */
    @TableField("v_sendType")
    private String vSendType;

    /**
     * 补充邮箱
     */
    @TableField("v_supplementEmail")
    private String vSupplementEmail;

    /**
     * 是否已读 0未读，1已读
     */
    @TableField("v_readflag")
    private String vReadFlag;

    @TableField("createByName")
    private String createByName;

    @TableField("createBy")
    private String createBy;

    @TableField("createOn")
    private Date createOn;

    @TableField("updateBy")
    private String updateBy;

    @TableField("updateOn")
    private Long updateOn;

    @TableField("record_version")
    private Integer recordVersion;

    @ApiModelProperty(value = "按公司设置，对应公司ID")
    @TableField("NCOMPANYID")
    private String nCompanyId;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    /**
     * 厂端人员信息（逗号分隔）
     */
    @TableField("vFReceiveUserName")
    private String vFReceiveUserName;

    /**
     * 经销商信息（逗号分隔）
     */
    @TableField("vDReceiveUserName")
    private String vDReceiveUserName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getvTitle() {
        return vTitle;
    }

    public void setvTitle(String vTitle) {
        this.vTitle = vTitle;
    }

    public String getvTypeId() {
        return vTypeId;
    }

    public void setvTypeId(String vTypeId) {
        this.vTypeId = vTypeId;
    }

    public String getvContext() {
        return vContext;
    }

    public void setvContext(String vContext) {
        this.vContext = vContext;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getvFactoryTitle() {
        return vFactoryTitle;
    }

    public void setvFactoryTitle(String vFactoryTitle) {
        this.vFactoryTitle = vFactoryTitle;
    }

    public String getvFactoryContext() {
        return vFactoryContext;
    }

    public void setvFactoryContext(String vFactoryContext) {
        this.vFactoryContext = vFactoryContext;
    }

    public String getvDealerTitle() {
        return vDealerTitle;
    }

    public void setvDealerTitle(String vDealerTitle) {
        this.vDealerTitle = vDealerTitle;
    }

    public String getvDealerContext() {
        return vDealerContext;
    }

    public void setvDealerContext(String vDealerContext) {
        this.vDealerContext = vDealerContext;
    }

    public String getvFBatchID() {
        return vFBatchID;
    }

    public void setvFBatchID(String vFBatchID) {
        this.vFBatchID = vFBatchID;
    }

    public String getvDBatchID() {
        return vDBatchID;
    }

    public void setvDBatchID(String vDBatchID) {
        this.vDBatchID = vDBatchID;
    }

    public String getSalesMailCheck() {
        return salesMailCheck;
    }

    public void setSalesMailCheck(String salesMailCheck) {
        this.salesMailCheck = salesMailCheck;
    }

    public String getServiceMailCheck() {
        return serviceMailCheck;
    }

    public void setServiceMailCheck(String serviceMailCheck) {
        this.serviceMailCheck = serviceMailCheck;
    }

    public String getIsAllDealer() {
        return isAllDealer;
    }

    public void setIsAllDealer(String isAllDealer) {
        this.isAllDealer = isAllDealer;
    }

    public String getIsAllFactory() {
        return isAllFactory;
    }

    public void setIsAllFactory(String isAllFactory) {
        this.isAllFactory = isAllFactory;
    }

    public String getvManagerid() {
        return vManagerid;
    }

    public void setvManagerid(String vManagerid) {
        this.vManagerid = vManagerid;
    }

    public String getvSendType() {
        return vSendType;
    }

    public void setvSendType(String vSendType) {
        this.vSendType = vSendType;
    }

    public String getvSupplementEmail() {
        return vSupplementEmail;
    }

    public void setvSupplementEmail(String vSupplementEmail) {
        this.vSupplementEmail = vSupplementEmail;
    }

    public String getvReadFlag() {
        return vReadFlag;
    }

    public void setvReadFlag(String vReadFlag) {
        this.vReadFlag = vReadFlag;
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateOn() {
        return createOn;
    }

    public void setCreateOn(Date createOn) {
        this.createOn = createOn;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getUpdateOn() {
        return updateOn;
    }

    public void setUpdateOn(Long updateOn) {
        this.updateOn = updateOn;
    }

    public Integer getRecordVersion() {
        return recordVersion;
    }

    public void setRecordVersion(Integer recordVersion) {
        this.recordVersion = recordVersion;
    }

    public String getnCompanyId() {
        return nCompanyId;
    }

    public void setnCompanyId(String nCompanyId) {
        this.nCompanyId = nCompanyId;
    }

    public Timestamp getDtstamp() {
        return dtstamp;
    }

    public void setDtstamp(Timestamp dtstamp) {
        this.dtstamp = dtstamp;
    }

    public String getvFReceiveUserName() {
        return vFReceiveUserName;
    }

    public void setvFReceiveUserName(String vFReceiveUserName) {
        this.vFReceiveUserName = vFReceiveUserName;
    }

    public String getvDReceiveUserName() {
        return vDReceiveUserName;
    }

    public void setvDReceiveUserName(String vDReceiveUserName) {
        this.vDReceiveUserName = vDReceiveUserName;
    }

    @Override
    public String toString() {
        return "MessageNoticePO{" +
                "id='" + id + '\'' +
                ", vTitle='" + vTitle + '\'' +
                ", vTypeId='" + vTypeId + '\'' +
                ", vContext='" + vContext + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", vFactoryTitle='" + vFactoryTitle + '\'' +
                ", vFactoryContext='" + vFactoryContext + '\'' +
                ", vDealerTitle='" + vDealerTitle + '\'' +
                ", vDealerContext='" + vDealerContext + '\'' +
                ", vFBatchID='" + vFBatchID + '\'' +
                ", vDBatchID='" + vDBatchID + '\'' +
                ", salesMailCheck='" + salesMailCheck + '\'' +
                ", serviceMailCheck='" + serviceMailCheck + '\'' +
                ", isAllDealer='" + isAllDealer + '\'' +
                ", isAllFactory='" + isAllFactory + '\'' +
                ", vManagerid='" + vManagerid + '\'' +
                ", vSendType='" + vSendType + '\'' +
                ", vSupplementEmail='" + vSupplementEmail + '\'' +
                ", vReadFlag='" + vReadFlag + '\'' +
                ", createByName='" + createByName + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createOn='" + createOn + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateOn='" + updateOn + '\'' +
                ", recordVersion='" + recordVersion + '\'' +
                ", nCompanyId='" + nCompanyId + '\'' +
                ", dtstamp='" + dtstamp + '\'' +
                ", vFReceiveUserName='" + vFReceiveUserName + '\'' +
                ", vDReceiveUserName='" + vDReceiveUserName + '\'' +
                "} " + super.toString();
    }
}
