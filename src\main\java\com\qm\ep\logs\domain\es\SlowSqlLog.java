package com.qm.ep.logs.domain.es;


import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;

/**
 * @description: es 慢sql日志实体
 * @author: tl
 * @time: 2021/04/13
 */
@Schema(description = "慢sql日志实体")
@Data
public class SlowSqlLog implements Serializable {

    private static final long serialVersionUID = 2073684414842102685L;

    /**
     * id
     */
    @Schema(description = "主键")
    @Id
    private String id;

    /**
     * DB
     */

    @Schema(description = "数据DB")
    @JsonAlias("db_ip")
    private String dbIP;

    /**
     * DB实例
     */
    @Schema(description = "数据DB实例")
    @JsonAlias("db_database")
    private String dbDatabase;


    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    @JsonAlias("log_time")
    private String logTime;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    @JsonAlias("sql_timestamp")
    private Long sqlTimestamp;


    /**
     * 耗时（ms）
     */
    @Schema(description = "耗时（ms）")
    @JsonAlias("query_time")
    private Integer queryTime;

    /**
     * SQL类型
     */
    @Schema(description = "数据SQL类型")
    @JsonAlias("sql_action")
    private String sqlAction;

    /**
     * SQL
     */
    @Schema(description = "数据SQL")
    @JsonAlias("sql")
    private String sql;

    /**
     * 访问账号
     */
    @Schema(description = "访问账号")
    @JsonAlias("db_user")
    private String dbUser;

    /**
     * Lock Time
     */
    @Schema(description = "数据Lock Time")
    @JsonAlias("lock_time")
    private Integer lockTime;

    /**
     * Row Sent
     */
    @Schema(description = "数据Row Sent")
    @JsonAlias("rows_sent")
    private Integer rowsSent;

    /**
     * Row Examined
     */
    @Schema(description = "数据Row Examined")
    @JsonAlias("rows_examined")
    private String rowsExamined;

    /**
     * Row Examined
     */
    @Schema(description = "数据Row Examined")
    @JsonAlias("client_ip")
    private String clientIp;

    /**
     * 日志
     */
    @Schema(description = "日志")
    @JsonAlias("message")
    private String message;


}
