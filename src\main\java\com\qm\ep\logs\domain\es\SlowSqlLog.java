package com.qm.ep.logs.domain.es;


import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;

/**
 * @description: es 慢sql日志实体
 * @author: tl
 * @time: 2021/04/13
 */
@Data
public class SlowSqlLog implements Serializable {

    private static final long serialVersionUID = 2073684414842102685L;

    /**
     * id
     */
    @Id
    private String id;

    /**
     * DB
     */

    @JsonAlias("db_ip")
    private String dbIP;

    /**
     * DB实例
     */
    @JsonAlias("db_database")
    private String dbDatabase;


    /**
     * 执行时间
     */
    @JsonAlias("log_time")
    private String logTime;

    /**
     * 执行时间
     */
    @JsonAlias("sql_timestamp")
    private Long sqlTimestamp;


    /**
     * 耗时（ms）
     */
    @JsonAlias("query_time")
    private Integer queryTime;

    /**
     * SQL类型
     */
    @JsonAlias("sql_action")
    private String sqlAction;

    /**
     * SQL
     */
    @JsonAlias("sql")
    private String sql;

    /**
     * 访问账号
     */
    @JsonAlias("db_user")
    private String dbUser;

    /**
     * Lock Time
     */
    @JsonAlias("lock_time")
    private Integer lockTime;

    /**
     * Row Sent
     */
    @JsonAlias("rows_sent")
    private Integer rowsSent;

    /**
     * Row Examined
     */
    @JsonAlias("rows_examined")
    private String rowsExamined;

    /**
     * Row Examined
     */
    @JsonAlias("client_ip")
    private String clientIp;

    /**
     * 日志
     */
    @JsonAlias("message")
    private String message;


}
