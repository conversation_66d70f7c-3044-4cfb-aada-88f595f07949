package com.qm.ep.logs.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: es 慢sql日志实体
 * @author: tl
 * @time: 2021/04/13
 */
@Data
public class SlowSqlLogDTO extends JsonParamDto implements Serializable {
    private static final long serialVersionUID = 2073684414842102685L;
    /**
     * id
     */
    private String id;

    /**
     * DB
     */

    @JsonAlias("db_ip")
    private String dbIP;

    /**
     * DB实例
     */
    @JsonAlias("db_database")
    private String dbDatabase;

    /**
     * DB实例
     */
    @JsonAlias("client_ip")
    private String clientIp;


    /**
     * 执行时间
     */
    @JsonAlias("log_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date logTime;

    /**
     * 耗时（ms）
     */
    @JsonAlias("query_time")
    private Integer queryTime;

    /**
     * SQL类型
     */
    @JsonAlias("sql_action")
    private String sqlAction;

    /**
     * SQL
     */
    @JsonAlias("sql")
    private String sql;

    /**
     * 访问账号
     */
    @JsonAlias("db_user")
    private String dbUser;


    /**
     * Lock Time
     */
    @JsonAlias("lock_time")
    private String lockTime;

    /**
     * Row Sent
     */
    @JsonAlias("rows_sent")
    private String rowsSent;

    /**
     * Row Examined
     */
    @JsonAlias("rows_examined")
    private String rowsExamined;

    /**
     * 日志
     */
    @JsonAlias("message")
    private String message;

    /**
     * 访问时间 开始时间
     */
    private String startTime;

    /**
     * 执行时间
     */
    private Long sqlTimestamp;


    /**
     * 访问时间 结束时间
     */
    private String endTime;
    /**
     * 访问时间 开始日期
     */
    private String startDateTime;

    /**
     * 访问时间 结束时间
     */
    private String endtDateTime;

    /**
     * 排序
     */
    private String sort;

}
