package com.qm.ep.logs.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: es 慢sql日志实体
 * @author: tl
 * @time: 2021/04/13
 */
@Schema(description = "慢sql日志实体")
@Data
public class SlowSqlLogDTO extends JsonParamDto implements Serializable {
    private static final long serialVersionUID = 2073684414842102685L;
    /**
     * id
     */
    @Schema(description = "主键")
    private String id;

    /**
     * DB
     */

    @Schema(description = "数据DB")
    @JsonAlias("db_ip")
    private String dbIP;

    /**
     * DB实例
     */
    @Schema(description = "数据DB实例")
    @JsonAlias("db_database")
    private String dbDatabase;

    /**
     * DB实例
     */
    @Schema(description = "数据DB实例")
    @JsonAlias("client_ip")
    private String clientIp;


    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    @JsonAlias("log_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date logTime;

    /**
     * 耗时（ms）
     */
    @Schema(description = "耗时（ms）")
    @JsonAlias("query_time")
    private Integer queryTime;

    /**
     * SQL类型
     */
    @Schema(description = "数据SQL类型")
    @JsonAlias("sql_action")
    private String sqlAction;

    /**
     * SQL
     */
    @Schema(description = "数据SQL")
    @JsonAlias("sql")
    private String sql;

    /**
     * 访问账号
     */
    @Schema(description = "访问账号")
    @JsonAlias("db_user")
    private String dbUser;


    /**
     * Lock Time
     */
    @Schema(description = "数据Lock Time")
    @JsonAlias("lock_time")
    private String lockTime;

    /**
     * Row Sent
     */
    @Schema(description = "数据Row Sent")
    @JsonAlias("rows_sent")
    private String rowsSent;

    /**
     * Row Examined
     */
    @Schema(description = "数据Row Examined")
    @JsonAlias("rows_examined")
    private String rowsExamined;

    /**
     * 日志
     */
    @Schema(description = "日志")
    @JsonAlias("message")
    private String message;

    /**
     * 访问时间 开始时间
     */
    @Schema(description = "访问时间 开始时间")
    private String startTime;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private Long sqlTimestamp;


    /**
     * 访问时间 结束时间
     */
    @Schema(description = "访问时间 结束时间")
    private String endTime;
    /**
     * 访问时间 开始日期
     */
    @Schema(description = "访问时间 开始日期")
    private String startDateTime;

    /**
     * 访问时间 结束时间
     */
    @Schema(description = "访问时间 结束时间")
    private String endtDateTime;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private String sort;

}
