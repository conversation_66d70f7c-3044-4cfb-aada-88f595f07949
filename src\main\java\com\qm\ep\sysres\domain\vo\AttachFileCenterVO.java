package com.qm.ep.sysres.domain.vo;

import com.qm.ep.sysres.domain.bean.AttachBusDO;
import com.qm.ep.sysres.domain.bean.AttachBusExtDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 按业务代码返回给前端的vo对象其中属性：
 * 事务码对应sysc080对象
 * 事务码对应的附件列表List<sysb080>
 * 事务码对应的文件类型List<sysc080d>
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@ApiModel(value = "AttachFileCenterVO对象", description = "按业务代码返回该业务代码对应的文件集合对象")
@Data
public class AttachFileCenterVO {
    @ApiModelProperty(value = "事务码对应sysc080对象")
    private AttachBusDO attachBusDO;

    @ApiModelProperty(value = "事务码对应的附件列表List<sysb080>")
    private List<AttachCenterFileVO> attachCenterFileVOList;

    @ApiModelProperty(value = "事务码对应的文件类型List<sysc080d>")
    private List<AttachBusExtDO> attachBusExtDOList;
}
