# Gateway服务JDK17兼容性修复方案

## 问题分析

从错误日志可以看出，问题出现在Gateway服务层面：

```
Connection has been closed
The body is not set. Did handling complete with success?
at org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage.getBody
```

这表明Gateway服务在转发请求到后端服务时，连接被意外关闭，导致请求体丢失。

## 根本原因

1. **Gateway使用WebFlux**：Spring Cloud Gateway基于WebFlux，使用Reactor Netty
2. **JDK17兼容性**：Reactor Netty在JDK17下的HTTP连接行为发生变化
3. **连接管理**：Gateway的HTTP客户端配置需要针对JDK17优化
4. **请求体处理**：CachedBodyOutputMessage在JDK17下处理大请求体时可能出现问题

## 修复方案

### 1. Gateway HTTP客户端配置

创建Gateway专用的HTTP客户端配置：

```java
@Configuration
public class GatewayJdk17HttpClientConfig {

    @Bean
    public HttpClient gatewayHttpClient() {
        return HttpClient.create()
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)
            .responseTimeout(Duration.ofSeconds(600))  // 支持长时间请求
            .doOnConnected(conn -> 
                conn.addHandlerLast(new ReadTimeoutHandler(600))
                    .addHandlerLast(new WriteTimeoutHandler(60)))
            // JDK17兼容性：配置连接池
            .connectionProvider(ConnectionProvider.builder("gateway-pool")
                .maxConnections(500)
                .maxIdleTime(Duration.ofSeconds(30))
                .maxLifeTime(Duration.ofMinutes(5))
                .pendingAcquireTimeout(Duration.ofSeconds(30))
                .evictInBackground(Duration.ofSeconds(120))
                .build())
            // JDK17兼容性：启用TCP Keep-Alive
            .option(ChannelOption.SO_KEEPALIVE, true)
            .option(ChannelOption.TCP_NODELAY, true);
    }

    @Bean
    public ReactiveLoadBalancerClientFilter loadBalancerClientFilter(
            LoadBalancerClientFactory clientFactory,
            GatewayLoadBalancerProperties properties,
            HttpClient httpClient) {
        return new ReactiveLoadBalancerClientFilter(clientFactory, properties) {
            @Override
            protected HttpClient getHttpClient() {
                return httpClient;
            }
        };
    }
}
```

### 2. Gateway配置文件修改

在Gateway服务的`application.yml`中添加：

```yaml
spring:
  cloud:
    gateway:
      httpclient:
        # JDK17兼容性：HTTP客户端配置
        connect-timeout: 30000
        response-timeout: 600s
        pool:
          type: elastic
          max-connections: 500
          max-idle-time: 30s
          max-life-time: 5m
          acquire-timeout: 30s
        # JDK17兼容性：启用压缩但限制大小
        compression: true
        # 禁用HTTP/2以避免兼容性问题
        use-insecure-trust-manager: false
      
      # 全局过滤器配置
      default-filters:
        - name: Retry
          args:
            retries: 2
            statuses: BAD_GATEWAY,GATEWAY_TIMEOUT
            methods: GET,POST
            backoff:
              firstBackoff: 2s
              maxBackoff: 10s
              factor: 2
              basedOnPreviousValue: false

# JDK17兼容性：Reactor Netty配置
reactor:
  netty:
    http:
      server:
        # 增加最大请求大小
        max-request-size: 100MB
        # 增加连接超时
        connection-timeout: 30s
      client:
        # 连接池配置
        pool:
          max-connections: 500
          max-idle-time: 30s
          max-life-time: 5m
        # 响应超时
        response-timeout: 600s

# 日志配置
logging:
  level:
    reactor.netty: DEBUG
    org.springframework.cloud.gateway: DEBUG
    com.qm.cloud.gateway: DEBUG
```

### 3. HttpRequestFilter修复

修改Gateway中的`HttpRequestFilter.java`：

```java
@Component
@Slf4j
public class HttpRequestFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().pathWithinApplication().value();
        
        log.info("---info---请求urlPath：{}", path);
        
        return chain.filter(exchange)
            .doOnError(throwable -> {
                log.error("---error--执行url{}异常：{}", 
                    request.getURI(), throwable.getMessage(), throwable);
            })
            // JDK17兼容性：添加错误恢复机制
            .onErrorResume(throwable -> {
                if (throwable instanceof IllegalStateException && 
                    throwable.getMessage().contains("The body is not set")) {
                    
                    log.warn("---warn--检测到JDK17兼容性问题，尝试恢复：{}", throwable.getMessage());
                    
                    // 返回适当的错误响应
                    ServerHttpResponse response = exchange.getResponse();
                    response.setStatusCode(HttpStatus.GATEWAY_TIMEOUT);
                    
                    String errorBody = "{\"error\":\"Gateway timeout - JDK17 compatibility issue\",\"code\":504}";
                    DataBuffer buffer = response.bufferFactory().wrap(errorBody.getBytes(StandardCharsets.UTF_8));
                    response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
                    
                    return response.writeWith(Mono.just(buffer));
                }
                
                // 其他异常继续抛出
                return Mono.error(throwable);
            });
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
```

### 4. 请求体缓存配置

创建专门的请求体处理配置：

```java
@Configuration
public class GatewayBodyConfig {

    @Bean
    public ModifyRequestBodyGatewayFilterFactory modifyRequestBodyFilter() {
        return new ModifyRequestBodyGatewayFilterFactory() {
            @Override
            public GatewayFilter apply(Config config) {
                return new GatewayFilter() {
                    @Override
                    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
                        ServerHttpRequest request = exchange.getRequest();
                        
                        // JDK17兼容性：对大请求体进行特殊处理
                        if (isLargeRequest(request)) {
                            return handleLargeRequest(exchange, chain);
                        }
                        
                        return chain.filter(exchange);
                    }
                };
            }
        };
    }
    
    private boolean isLargeRequest(ServerHttpRequest request) {
        String contentLength = request.getHeaders().getFirst("Content-Length");
        if (contentLength != null) {
            try {
                long length = Long.parseLong(contentLength);
                return length > 1024 * 1024; // 1MB
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return false;
    }
    
    private Mono<Void> handleLargeRequest(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 对大请求体使用流式处理，避免缓存整个请求体
        return chain.filter(exchange)
            .timeout(Duration.ofSeconds(600)) // 增加超时时间
            .doOnError(throwable -> {
                log.error("---error--大请求处理失败：{}", throwable.getMessage(), throwable);
            });
    }
}
```

## 部署步骤

### 1. 备份Gateway配置
```bash
# 备份Gateway服务配置
cp gateway-service/src/main/resources/application.yml gateway-service/src/main/resources/application.yml.backup
```

### 2. 应用修复
1. 添加上述Java配置类到Gateway项目
2. 更新application.yml配置
3. 修改HttpRequestFilter

### 3. 测试验证
```bash
# 重启Gateway服务
# 测试导出功能
curl -X POST http://gateway:port/sal-query/searchInvocie/table \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}' \
  --max-time 600
```

## 监控要点

1. **连接池使用情况**
2. **请求超时频率**
3. **"The body is not set"错误数量**
4. **Gateway内存使用**

## 回滚方案

如果修复后出现问题：

1. **快速回滚**：
```bash
cp application.yml.backup application.yml
# 移除新增的Java配置类
# 重启Gateway服务
```

2. **渐进回滚**：
   - 先回滚超时配置
   - 再回滚连接池配置
   - 最后回滚过滤器修改

## 注意事项

1. **Gateway和后端服务**：两者都需要JDK17兼容性修复
2. **超时配置一致性**：Gateway的超时应该略大于后端服务
3. **连接池大小**：根据实际并发量调整
4. **监控告警**：设置关键指标的监控告警

这个修复方案需要在Gateway服务中实施，与sys-res服务的修复配合使用，才能彻底解决JDK17兼容性问题。
