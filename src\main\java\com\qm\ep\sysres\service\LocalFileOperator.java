package com.qm.ep.sysres.service;

import com.qm.tds.base.domain.vo.UploadFileVO;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface LocalFileOperator {
    String FILE_SEPARATOR = "/";
    String THUMBNAIL_SMALL = "small";
    String THUMBNAIL_NORMAL = "normal";

    boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int width, int height, UploadFileVO uploadFileVO);

    boolean downloadFile(HttpServletResponse response, String vaddr) throws IOException;

    String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile);

}
