package com.qm.ep.sysres.integration;

import com.qm.ep.sysres.service.ITableService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JDK17兼容性集成测试
 * 验证表格导出功能在JDK17下的正常工作
 */
@SpringBootTest
@ActiveProfiles("local")
class TableExportJdk17Test {

    @Autowired
    private ITableService tableService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ClientHttpRequestFactory clientHttpRequestFactory;

    @Test
    void testHttpClientConfiguration() {
        // 验证RestTemplate配置正确
        assertNotNull(restTemplate);
        assertNotNull(clientHttpRequestFactory);
        
        // 验证是否使用了正确的HTTP客户端工厂
        assertTrue(clientHttpRequestFactory.getClass().getSimpleName().contains("HttpComponents"));
    }

    @Test
    void testTableExportBasicFunctionality() {
        // 创建基本的导出参数
        Map<String, Object> exportInfo = createBasicExportInfo();
        
        try {
            // 执行导出（这里可能会调用实际的远程服务，需要根据测试环境调整）
            Map<String, Object> result = tableService.doExport(exportInfo);
            
            // 验证结果不为空
            assertNotNull(result);
            
            // 如果有错误，验证错误信息不包含JDK17兼容性问题
            if (result.containsKey("errMsg")) {
                String errorMsg = result.get("errMsg").toString();
                assertFalse(errorMsg.contains("insufficient data written"), 
                    "仍然存在JDK17兼容性问题: " + errorMsg);
                assertFalse(errorMsg.contains("Connection has been closed"), 
                    "仍然存在连接关闭问题: " + errorMsg);
            }
            
        } catch (Exception e) {
            // 验证异常不是JDK17兼容性相关的
            String errorMessage = e.getMessage();
            assertFalse(errorMessage.contains("insufficient data written"), 
                "仍然存在JDK17兼容性问题: " + errorMessage);
            assertFalse(errorMessage.contains("Connection has been closed"), 
                "仍然存在连接关闭问题: " + errorMessage);
            
            // 如果是其他类型的异常（如网络不可达），这是可以接受的
            System.out.println("测试中遇到的异常（可能是环境相关）: " + errorMessage);
        }
    }

    /**
     * 创建基本的导出信息用于测试
     */
    private Map<String, Object> createBasicExportInfo() {
        Map<String, Object> exportInfo = new HashMap<>();
        exportInfo.put("vtranscode", "TEST_JDK17");
        exportInfo.put("vmenuname", "JDK17兼容性测试");
        exportInfo.put("fileName", "jdk17_test.xlsx");
        exportInfo.put("serviceName", "/test/data");
        exportInfo.put("pageSize", 10);
        
        // 创建工作表配置
        List<Map<String, Object>> sheets = new ArrayList<>();
        Map<String, Object> sheet = new HashMap<>();
        sheet.put("sheetName", "TestSheet");
        
        List<Map<String, Object>> columns = new ArrayList<>();
        Map<String, Object> column1 = new HashMap<>();
        column1.put("fieldName", "id");
        column1.put("fieldLabel", "ID");
        column1.put("width", "100");
        column1.put("useDict", false);
        columns.add(column1);
        
        Map<String, Object> column2 = new HashMap<>();
        column2.put("fieldName", "name");
        column2.put("fieldLabel", "名称");
        column2.put("width", "200");
        column2.put("useDict", false);
        columns.add(column2);
        
        sheet.put("sheetColumns", columns);
        sheets.add(sheet);
        
        exportInfo.put("sheets", sheets);
        exportInfo.put("includeHeader", true);
        
        return exportInfo;
    }

    @Test
    void testHttpClientTimeoutConfiguration() {
        // 这个测试验证HTTP客户端的超时配置是否合理
        // 通过检查配置来确保不会出现过长的超时时间
        
        // 注意：这里我们主要验证配置的存在性，具体的超时值测试需要实际的网络环境
        assertNotNull(clientHttpRequestFactory);
        
        // 验证工厂类型
        String factoryClassName = clientHttpRequestFactory.getClass().getSimpleName();
        assertTrue(factoryClassName.contains("HttpComponents"), 
            "应该使用HttpComponentsClientHttpRequestFactory，实际使用: " + factoryClassName);
    }
}
