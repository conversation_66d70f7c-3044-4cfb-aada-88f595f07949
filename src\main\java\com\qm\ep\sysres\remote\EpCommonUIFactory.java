package com.qm.ep.sysres.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EpCommonUIFactory implements FallbackFactory<EpCommonUIRemote> {

    @Override
    public EpCommonUIRemote create(Throwable throwable) {
        EpCommonUIRemoteHystrix epMirrorOperatorRemoteHystrix = new EpCommonUIRemoteHystrix();
        epMirrorOperatorRemoteHystrix.setHystrixEx(throwable);
        return epMirrorOperatorRemoteHystrix;
    }
}
