#这里填写法语翻译
MSG.sysres.common.uploadSuccess=Téléchargé avec succès !
ERR.sysres.common.uploadFail=Le téléchargement a échoué !
MSG.sysres.common.copySuccess=Copier avec succès
MSG.sysres.common.msgSendSuccess=Message envoyé avec succès
MSG.sysres.websocket.setSender=Merci de préciser l'expéditeur !
MSG.sysres.pdfFileImpl.localTitle=Source de données statistiques : cas d'utilisation "système EP - journal unifié - analyse des performances de l'interface"
ERR.sysres.tableService.getPageFail=Echec de l'exécution de la pagination doExport pour obtenir les données :
MSG.sysres.tableService.serviceNotFound=Service %s introuvable.
MSG.sysres.tableService.enterDownload=Veuillez cliquer sur %s pour télécharger le fichier.
MSG.sysres.tableService.exportFinish=Exportation terminée
##################################=
ERR.sysres.WebSocketDto.msgNull=Envoyer le message ne peut pas être vide
ERR.sysres.AttachCopyBillVO.originTicketIdNull=L'ID du document d'origine ne peut pas être vide
ERR.sysres.AttachCopyBillVO.newTicketIdNull=Le nouvel ID de document ne peut pas être vide
MSG.sysres.WebSocketCServer.mqSendFail=La distribution des messages MQ a échoué.
MSG.sysres.WebSocketCServer.heartbeatResponse=Réponse de battement de coeur
MSG.sysres.PdfFileImpl.commonLogCount=Source de données statistiques : cas d'utilisation "système EP - journal unifié - analyse des performances de l'interface"
MSG.sysres.PdfFileImpl.createBy=Créé par %s
MSG.sysres.PdfFileImpl.checker=Vérificateur
MSG.sysres.PdfFileImpl.checkTime=Vérifier l'heure
MSG.sysres.PdfFileImpl.countPeriod=Période statistique
MSG.sysres.PdfFileImpl.systemEnv=Environnement système
MSG.sysres.PdfFileImpl.performanceCheckList=1. Liste de vérification des performances de l'interface
MSG.sysres.PdfFileImpl.averageCostTimeTop=Temps moyen passé Top10
MSG.sysres.PdfFileImpl.serverName=Nom du service
MSG.sysres.PdfFileImpl.interfaceUri=URI de l'interface
MSG.sysres.PdfFileImpl.successRate=Taux de réussite
MSG.sysres.PdfFileImpl.averageCostTime=Temps moyen (ms)
MSG.sysres.PdfFileImpl.runTimes=Nombre d'exécutions (fois)
MSG.sysres.PdfFileImpl.handler=Gestionnaire
MSG.sysres.PdfFileImpl.handleTime=Temps de traitement
MSG.sysres.PdfFileImpl.remarks=Remarque
MSG.sysres.PdfFileImpl.costTimeTop=Top10 chronophage
MSG.sysres.PdfFileImpl.costTime=Temps d'exécution
MSG.sysres.PdfFileImpl.costTimems=Temps d'exécution (ms)
MSG.sysres.PdfFileImpl.exceptionTop=1.3. Top 10 des anomalies de service
MSG.sysres.PdfFileImpl.exceptionInfo=Informations sur les exceptions
MSG.sysres.PdfFileImpl.exceptionAccount=Nombre d'exceptions
MSG.sysres.PdfFileImpl.searchHelpPerformanceList=2. Liste de contrôle des performances de l'aide à la recherche
MSG.sysres.PdfFileImpl.searchHelpName=Rechercher le nom de l'aide
MSG.sysres.PdfFileImpl.databasePerformanceList=3. Liste de contrôle des performances d'exécution de la base de données
MSG.sysres.PdfFileImpl.showSqlTop=3.1. SqlTop10 lent
MSG.sysres.PdfFileImpl.showSqlCount=Source de données statistiques : journal SQl lent de la base de données, cas d'utilisation "système EP - journal unifié - journal SQl lent"
MSG.sysres.PdfFileImpl.clientIp=IP du client
MSG.sysres.PdfFileImpl.databaseAddress=Adresse de base de données
MSG.sysres.PdfFileImpl.databaseInstance=Instance de base de données
MSG.sysres.PdfFileImpl.returnRecordCount=Nombre d'enregistrements renvoyés
MSG.sysres.PdfFileImpl.quartzTask=4. Tâches planifiées
MSG.sysres.PdfFileImpl.exceptionCount=4.1. Top 10 des statistiques anormales
MSG.sysres.PdfFileImpl.quartzLogCount=Source des données statistiques : "EP system - unified log - scheduled task log"
MSG.sysres.PdfFileImpl.name=Nom
MSG.sysres.PdfFileImpl.failTimes=Nombre d'échecs
MSG.sysres.PdfFileImpl.execCountTop=4.2. Top 10 des statistiques des dirigeants
MSG.sysres.PdfFileImpl.execTimes=Nombre d'exécutions
MSG.sysres.MyHeaderFooter.pageStartEnd=Je suis en-tête/pied de page
MSG.sysres.MyHeaderFooter.currentPage=Pages %s/
MSG.sysres.MyHeaderFooter.totalPage=Nombre total de pages %s
#############################=
ERR.message.MessageModuleEnum.mobile=Numéro de téléphone
ERR.message.MessageModuleEnum.emailAddress=Adresse e-mail
ERR.message.MessageModuleEnum.wxAccount=Numéro de WeChat
ERR.message.MessageModuleEnum.dingAccount=Numéro de DingTalk
ERR.message.DingTemplateDTO.messageTitleNull=Le titre du message ne peut pas être vide !
ERR.message.DingTemplateDTO.messageContentNull=Le contenu du message ne peut pas être vide !
ERR.message.DingTemplateDTO.redirectUrlNull=L'URL de saut ne peut pas être vide !
ERR.message.DingTemplateDTO.methodContent202=202 façons de contenu !
ERR.message.MessageTemplateSendDTO.publishDateNull=La date de sortie ne peut pas être vide
ERR.message.MessageTemplateSendDTO.objectDetailNull=Les détails de l'objet ne peuvent pas être vides
ERR.message.MessageTemplateSendUserDTO.busiSysCodeNull=Le numéro de système d'entreprise ne peut pas être vide
ERR.message.SendDingDTO.dingIdNull=L'ID utilisateur DingTalk ne peut pas être vide !
ERR.message.SendDingEPDTO.epsysIdNull=L'ID utilisateur du système EP ne peut pas être vide !
ERR.message.SendDingEPDTO.epsysCompanyIdNull=L'ID de l'entreprise de l'ID utilisateur du système EP !
ERR.message.SendMailDTO.emailReceiverNull=L'adresse du destinataire de l'e-mail ne peut pas être vide !
ERR.message.SendSmsDTO.smsReceiverNull=L'adresse du destinataire du sms ne peut pas être vide !
ERR.message.SendSmsDTO.templateCodeNull=L'ID de modèle ne peut pas être vide !
ERR.message.SendWxTemplateDTO.wxReceiverAddressNull=L'adresse du destinataire WeChat ne peut pas être vide !
ERR.message.SendWxTemplateDTO.templateContentNull=Le contenu du modèle ne peut pas être vide !
ERR.message.VerificationDTO.mobileNull=Le numéro de téléphone ne peut pas être vide !
ERR.message.MessageFactory.messageFactoryNoTemplate=La fabrique de messages ne définit pas de modèle :