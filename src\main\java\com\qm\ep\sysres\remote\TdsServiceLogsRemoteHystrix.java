package com.qm.ep.sysres.remote;

import com.qm.ep.logs.domain.dto.*;
import com.qm.ep.logs.domain.es.GateWayLog;
import com.qm.ep.logs.domain.es.SlowSqlLog;
import com.qm.ep.logs.domain.vo.SearchVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.api.mp.pagination.QmPage;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21
 */
@Component
public class TdsServiceLogsRemoteHystrix extends QmRemoteHystrix<TdsServiceLogsRemote> implements TdsServiceLogsRemote {

    @Override
    public JsonResultVo<List<Map<String, String>>> timeConsumingAnalysis(@RequestBody AnalyseDto analyseDto) {
        return getResult();
    }

    @Override
    public JsonResultVo<QmPage<GateWayLog>> save(@RequestBody GateWayLogDTO gateWayLogDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo<List<Map<String, String>>> timeConsumingAnalysis(@RequestBody SysErrorDTO sysErrorDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo<List<Map<String, String>>> searchHelpAnalysis(@RequestBody SearchHelpAnalyseDto searchHelpAnalyseDto) {
        return getResult();
    }

    @Override
    public JsonResultVo<QmPage<SearchVO>> searchQueryTime(@RequestBody GateWayLogDTO gateWayLogDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo<QmPage<SlowSqlLog>> query(@RequestBody SlowSqlLogDTO slowSqlLogDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo offlineByUser(String companyid, String operatorid, String operatorname, String personcode, String userId) {
        return getResult();
    }
}
