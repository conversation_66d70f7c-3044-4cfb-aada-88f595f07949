<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.sysres.mapper.AttachFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <id column="ID" property="id"/>
        <result column="VBUSTYPE" property="vbustype"/>
        <result column="NCO" property="nco"/>
        <result column="NBILLID" property="nbillid"/>
        <result column="VFILENAME" property="vfilename"/>
        <result column="VEXTENSION" property="vextension"/>
        <result column="VITEM" property="vitem"/>
        <result column="VREMARK" property="vremark"/>
        <result column="NOPR" property="nopr"/>
        <result column="DUP" property="dup"/>
        <result column="VADDR" property="vaddr"/>
        <result column="VCONTENTTYPE" property="vcontenttype"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, VBUSTYPE, NCO, NBILLID, VFILENAME, VEXTENSION, VITEM, VREMARK, NOPR, DUP, VADDR, VCONTENTTYPE, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.VBUSTYPE,
                a.NCO,
                a.NBILLID,
                a.VFILENAME,
                a.VEXTENSION,
                a.VITEM,
                a.VREMARK,
                a.NOPR,
                a.DUP,
                a.VADDR,
                a.VCONTENTTYPE,
                a.DTSTAMP,
                a.ID
            from sysb080 a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.sysres.domain.bean.AttachFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="getAttachCenterFile" resultType="com.qm.ep.sysres.domain.vo.AttachCenterFileVO">
        select
        t.ID,
        t.VBUSTYPE,
        t.NCO,
        t.NBILLID,
        t.VFILENAME,
        t.VEXTENSION,
        t.VITEM,
        t.VREMARK,
        t.NOPR,
        c030.VPERSONNAME,
        t.DUP,
        t.VADDR,
        t.VCONTENTTYPE,
        t.DTSTAMP,
        c082.VFILETYPE,
        c081.VICON,
        t.vitffulfilename
        from sysb080 t
        left join sysc082 c082 on c082.VEXTENSION = t.VEXTENSION
        left join sysc081 c081 on c081.VFILETYPE = c082.VFILETYPE
        left join sysc030 c030 on c030.id = t.NOPR
        <where>
            <if test="vbustype != null and vbustype != ''">
                AND t.VBUSTYPE = #{vbustype}
            </if>
            <if test="nbillid != null and nbillid != ''">
                AND t.NBILLID = #{nbillid}
            </if>
            <if test="id != null and id != ''">
                AND t.ID = #{id}
            </if>
        </where>
        order by t.DTSTAMP DESC
    </select>


    <select id="getfilelist" resultType="com.qm.ep.sysres.domain.vo.AttachCenterFileVO">
        select
                a.VBUSTYPE,
                a.NCO,
                a.NBILLID,
                a.VFILENAME,
                a.VEXTENSION,
                a.VITEM,
                a.VREMARK,
                a.NOPR,
                a.DUP,
                a.VADDR,
                a.VCONTENTTYPE,
                a.DTSTAMP,
                a.ID
            from sysb080 a
    </select>

    <select id="getfilelistByBillid" resultType="com.qm.ep.sysres.domain.vo.AttachCenterFileVO">
        select
                a.VBUSTYPE,
                a.NCO,
                a.NBILLID,
                a.VFILENAME,
                a.VEXTENSION,
                a.VITEM,
                a.VREMARK,
                a.NOPR,
                a.DUP,
                a.VADDR,
                a.VCONTENTTYPE,
                a.DTSTAMP,
                a.ID
            from sysb080 a
            where nbillid =#{id}
    </select>

    <select id="getAttachCenterFileByAgent" resultType="com.qm.ep.sysres.domain.vo.AttachCenterFileVO">
        select
        t.ID,
        t.VBUSTYPE,
        t.NCO,
        t.NBILLID,
        t.VFILENAME,
        t.VEXTENSION,
        t.VITEM,
        t.VREMARK,
        t.NOPR,
        t.DUP,
        t.VADDR,
        t.VCONTENTTYPE,
        t.DTSTAMP,
        c082.VFILETYPE,
        c081.VICON,
        t.vitffulfilename
        from sysb080 t
        left join sysc082 c082 on c082.VEXTENSION = t.VEXTENSION
        left join sysc081 c081 on c081.VFILETYPE = c082.VFILETYPE
        <where>
            <if test="id != null and id != ''">
                AND t.ID = #{id}
            </if>
        </where>
        order by t.DTSTAMP DESC
    </select>


</mapper>
