package com.qm.ep.sysres.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 超时配置属性
 * 支持不同环境和不同类型请求的超时配置
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Component
@ConfigurationProperties(prefix = "app.timeout")
public class TimeoutProperties {

    /**
     * HTTP客户端超时配置
     */
    private HttpTimeout http = new HttpTimeout();

    /**
     * 导出功能超时配置
     */
    private ExportTimeout export = new ExportTimeout();

    /**
     * 重试配置
     */
    private RetryConfig retry = new RetryConfig();

    public static class HttpTimeout {
        /**
         * 连接超时（秒）
         */
        private int connect = 30;

        /**
         * 连接请求超时（秒）
         */
        private int connectionRequest = 30;

        /**
         * 响应超时（秒）
         */
        private int response = 600;

        // Getters and Setters
        public int getConnect() {
            return connect;
        }

        public void setConnect(int connect) {
            this.connect = connect;
        }

        public int getConnectionRequest() {
            return connectionRequest;
        }

        public void setConnectionRequest(int connectionRequest) {
            this.connectionRequest = connectionRequest;
        }

        public int getResponse() {
            return response;
        }

        public void setResponse(int response) {
            this.response = response;
        }
    }

    public static class ExportTimeout {
        /**
         * 小数据量导出超时（秒）
         */
        private int small = 300;

        /**
         * 中等数据量导出超时（秒）
         */
        private int medium = 600;

        /**
         * 大数据量导出超时（秒）
         */
        private int large = 900;

        /**
         * 超大数据量导出超时（秒）
         */
        private int extraLarge = 1800;

        // Getters and Setters
        public int getSmall() {
            return small;
        }

        public void setSmall(int small) {
            this.small = small;
        }

        public int getMedium() {
            return medium;
        }

        public void setMedium(int medium) {
            this.medium = medium;
        }

        public int getLarge() {
            return large;
        }

        public void setLarge(int large) {
            this.large = large;
        }

        public int getExtraLarge() {
            return extraLarge;
        }

        public void setExtraLarge(int extraLarge) {
            this.extraLarge = extraLarge;
        }
    }

    public static class RetryConfig {
        /**
         * 最大重试次数
         */
        private int maxAttempts = 2;

        /**
         * 初始重试延迟（毫秒）
         */
        private int initialDelay = 2000;

        /**
         * 重试延迟倍数
         */
        private double multiplier = 1.5;

        /**
         * 最大重试延迟（毫秒）
         */
        private int maxDelay = 10000;

        // Getters and Setters
        public int getMaxAttempts() {
            return maxAttempts;
        }

        public void setMaxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
        }

        public int getInitialDelay() {
            return initialDelay;
        }

        public void setInitialDelay(int initialDelay) {
            this.initialDelay = initialDelay;
        }

        public double getMultiplier() {
            return multiplier;
        }

        public void setMultiplier(double multiplier) {
            this.multiplier = multiplier;
        }

        public int getMaxDelay() {
            return maxDelay;
        }

        public void setMaxDelay(int maxDelay) {
            this.maxDelay = maxDelay;
        }
    }

    // Main class getters and setters
    public HttpTimeout getHttp() {
        return http;
    }

    public void setHttp(HttpTimeout http) {
        this.http = http;
    }

    public ExportTimeout getExport() {
        return export;
    }

    public void setExport(ExportTimeout export) {
        this.export = export;
    }

    public RetryConfig getRetry() {
        return retry;
    }

    public void setRetry(RetryConfig retry) {
        this.retry = retry;
    }

    /**
     * 根据数据量估算获取合适的超时时间
     * 
     * @param estimatedDataSize 估算的数据量
     * @return 超时时间（秒）
     */
    public int getTimeoutByDataSize(int estimatedDataSize) {
        if (estimatedDataSize <= 1000) {
            return export.getSmall();
        } else if (estimatedDataSize <= 5000) {
            return export.getMedium();
        } else if (estimatedDataSize <= 20000) {
            return export.getLarge();
        } else {
            return export.getExtraLarge();
        }
    }
}
