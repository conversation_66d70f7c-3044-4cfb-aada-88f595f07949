package com.qm.ep.sysres.config;

import com.qm.ep.sysres.constant.SocketConstant;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * @<PERSON> <PERSON><PERSON><PERSON>
 * @Date 2021/1/2$ 11:00$
 **/
@Slf4j
@Component
public class StartAfterApplicationRunner implements CommandLineRunner {

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void run(String... args) {
        log.info("清除REDIS内所有用户....");
        String redisKey = redisUtils.keyBuilder("Socket", SocketConstant.SOCKET_USER, "*");
        redisUtils.del(redisUtils.getKeys(redisKey));
    }
}
