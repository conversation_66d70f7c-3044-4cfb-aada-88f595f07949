package com.qm.ep.sysres.service;

import com.qm.ep.logs.domain.dto.*;
import com.qm.ep.logs.domain.es.GateWayLog;
import com.qm.ep.logs.domain.es.SlowSqlLog;
import com.qm.ep.logs.domain.vo.SearchVO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.ep.sysres.domain.bean.AttachFileDO;
import com.qm.ep.sysres.domain.vo.AttachCopyBillVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.base.domain.LoginKeyDO;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 按业务单据记录对应附件信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
public interface AttachFileService extends IQmBaseService<AttachFileDO> {

    /**
     * 后台上传附件
     *
     * @param multipartFile 附件信息
     * @param billId        业务单据ID
     * @param busType       业务类型。区分文件夹
     * @param compress      对图片是否压缩的标识符。1压缩，其他不压缩
     * @return 附件信息
     */
    AttachFileDO upload(MultipartFile multipartFile, String billId, String busType, String compress);

    /**
     * @return
     * @description 上传文件
     * <AUTHOR>
     * @date 2020/11/12 18:09
     */
    AttachFileDO upload(MultipartHttpServletRequest request) throws IOException;

    /**
     * @description 下载文件
     * <AUTHOR>
     * @date 2020/11/12 18:56
     */
    boolean download(HttpServletRequest req, HttpServletResponse res) throws IOException;

    /**
     * 复制附件数据
     *
     * @param copyParam 单据复制相关信息
     * @param userInfo  用户信息
     * @return 附件数据列表
     */
    List<AttachFileDO> copy(AttachCopyBillVO copyParam, LoginKeyDO userInfo);

    /**
     * 接口性能分析
     *
     * @param analyseDto
     * @return
     */
    JsonResultVo<List<Map<String, String>>> timeConsumingAnalysis(AnalyseDto analyseDto);

    /**
     * 查询网关日志
     *
     * @param gateWayLogDTO
     * @return
     */
    JsonResultVo<QmPage<GateWayLog>> gatewayLogsQuery(GateWayLogDTO gateWayLogDTO);

    /**
     * 异常分析
     *
     * @param sysErrorDTO
     * @return
     */
    JsonResultVo<List<Map<String, String>>> sysErrorTimeConsumingAnalysis(SysErrorDTO sysErrorDTO);

    /**
     * 接口性能分析
     *
     * @param searchHelpAnalyseDto
     * @return
     */
    JsonResultVo<List<Map<String, String>>> searchHelpAnalysis(SearchHelpAnalyseDto searchHelpAnalyseDto);

    /**
     * 查询网关日志
     *
     * @param gateWayLogDTO
     * @return
     */
    JsonResultVo<QmPage<SearchVO>> searchQueryTime(GateWayLogDTO gateWayLogDTO);

    /**
     * 查询慢sql日志
     *
     * @param slowSqlLogDTO
     * @return
     */
    JsonResultVo<QmPage<SlowSqlLog>> slowSqlLogQuery(SlowSqlLogDTO slowSqlLogDTO);

    /**
     * 执行统计
     *
     * @param dto
     * @return
     */
    JsonResultVo executionStatistics(QuartzDTO dto);

    /**
     * 待分页的执行统计
     *
     * @param dto
     * @return
     */
    JsonResultVo<QmPage<QuartzStatisticsVO>> executionStatisticsQuery(QuartzDTO dto);

    /**
     * 通过文件id获取文件对象
     * @param fileId
     * @return
     */
    byte[] getAttachFile(HttpServletRequest req, HttpServletResponse response, String fileId);
}
