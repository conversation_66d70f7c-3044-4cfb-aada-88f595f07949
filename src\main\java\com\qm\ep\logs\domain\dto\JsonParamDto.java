package com.qm.ep.logs.domain.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.HashMap;

/**
 * TDS项目入参基类。
 * <p>
 * 示例数据：
 * {
 * "theadFilter": {
 * "name|text": "姓名",
 * "createOn|range-date": "2020-05-11#2020-05-20"
 * },
 * "tsortby": "name|ascend,createOn|descend",
 * "twhere": "dstop > '2020-07-15' and dstop < '2020-07-16' and vsex in ['1','2'] and vrealName like ''a'a' or vpersoncode like '11' and ( vrealName like 'uu' or vstop in ['1'] )"
 * "currentPage": 1,
 * "pageSize": 20,
 * "vpersoncode": "aaa",
 * "vpersonname": "bbbb"
 * }
 */
@Data
@SuppressWarnings("squid:S1068")
public class JsonParamDto {

    /**
     * 过滤字符串
     * @deprecated 使用<code>twhere</code>替代
     */
    @Deprecated
    @Schema(title = "过滤字符串", description = "过滤字符串")
    private HashMap<String, String> theadFilter;

    /**
     * 排序信息。
     */
    @Schema(title = "排序信息", description = "排序信息")
    private String tsortby;

    /**
     * 过滤字符串
     */
    @Schema(title = "过滤字符串", description = "过滤字符串")
    private String twhere;

    /**
     * 当前页码
     */
    @Schema(title = "当前页码", description = "当前页码")
    private int currentPage;

    /**
     * 每页的记录数
     */
    @Schema(title = "每页的记录数", description = "每页的记录数")
    private int pageSize;


}
