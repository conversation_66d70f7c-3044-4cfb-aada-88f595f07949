package com.qm.ep.sysres.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import ws.schild.jave.Encoder;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;
import ws.schild.jave.encode.VideoAttributes;
import ws.schild.jave.info.AudioInfo;
import ws.schild.jave.info.VideoInfo;
import ws.schild.jave.info.VideoSize;

import java.io.*;
import java.nio.file.Files;

@Slf4j
public class VideoUtils{

    private VideoUtils(){
    }
    @SuppressWarnings("squid:S4042")
    public static MultipartFile compressionVideoFile(MultipartFile multipartFile, String fileName, String savePath, int bitRate){
        if(null==multipartFile){
            log.info("---error--"+"视频源文件不存在!");
            return null;
        }
        if(StringUtils.isBlank(savePath)){
            savePath = "/home/<USER>/upload_tmp/uploadFile";
        }
        File saveDir = new File(savePath);
        if(!saveDir.exists()){
            saveDir.mkdirs();
        }
        //服务器端生成临时视频文件
        File source =  compressionTmpVideo(multipartFile, fileName, savePath);
        //压缩视频
        File target = compressionEvtVideo(source,fileName,savePath,bitRate);
        if(null==target){
            return multipartFile;
        }
        try(FileInputStream fileInputStream = new FileInputStream(target)){
            MultipartFile newMultipartFile = new MockMultipartFile(multipartFile.getName(),multipartFile.getOriginalFilename(),
                    multipartFile.getContentType(),fileInputStream);
            return newMultipartFile;
        }catch (Exception e){
            log.info("---error--"+fileName+"压缩异常："+e.getMessage());
        }finally {
          if(target.length()>0&&target.delete()){
             log.info("源文件已删除！");
          }
        }
        return multipartFile;
    }

    @SuppressWarnings("squid:S4042")
    public static File compressionEvtVideo(File source,String fileName, String savePath, int bitRate) {
        if(source == null){
            log.warn("视频源文件不存在");
            return null;
        }
        File target = new File(savePath + File.separator + fileName);
        try {
            MultimediaObject object = new MultimediaObject(source);
            AudioInfo audioInfo = object.getInfo().getAudio();
            // 根据视频大小来判断是否需要进行压缩,
            long time = System.currentTimeMillis();
            //视频属性设置
            int maxBitRate = 128000;
            int maxSamplingRate = 44100;
            int maxFrameRate = 20;
            int maxWidth = 1280;

            AudioAttributes audio = new AudioAttributes();
            // 设置通用编码格式10
            // 设置音频比特率,单位:b (比特率越高，清晰度/音质越好，当然文件也就越大 128000 = 182kb)
            if(audioInfo.getBitRate() > maxBitRate){
                audio.setBitRate(maxBitRate);
            }

            // 设置重新编码的音频流中使用的声道数（1 =单声道，2 = 双声道（立体声））。如果未设置任何声道值，则编码器将选择默认值 0。
            audio.setChannels(audioInfo.getChannels());
            // 采样率越高声音的还原度越好，文件越大
            // 设置音频采样率，单位：赫兹 hz
            if(audioInfo.getSamplingRate() > maxSamplingRate){
                audio.setSamplingRate(maxSamplingRate);
            }

            //视频编码属性配置
            VideoInfo videoInfo = object.getInfo().getVideo();
            VideoAttributes video = new VideoAttributes();
            video.setCodec("h264");
            //设置音频比特率,单位:b (比特率越高，清晰度/音质越好，当然文件也就越大 800000 = 800kb)
            if(videoInfo.getBitRate() > bitRate){
                video.setBitRate(bitRate);
            }

            // 视频帧率：15 f / s  帧率越低，效果越差
            // 设置视频帧率（帧率越低，视频会出现断层，越高让人感觉越连续），视频帧率（Frame rate）是用于测量显示帧数的量度。所谓的测量单位为每秒显示帧数(Frames per Second，简：FPS）或“赫兹”（Hz）。
            if(videoInfo.getFrameRate() > maxFrameRate){
                video.setFrameRate(maxFrameRate);
            }

            // 限制视频宽高
            int width = videoInfo.getSize().getWidth();
            int height = videoInfo.getSize().getHeight();
            if(width > maxWidth){
                float rat = (float) width / maxWidth;
                video.setSize(new VideoSize(maxWidth,(int)(height/rat)));
            }

            EncodingAttributes attr = new EncodingAttributes();
            attr.setAudioAttributes(audio);
            attr.setVideoAttributes(video);
            Encoder encoder = new Encoder();
            encoder.encode(new MultimediaObject(source), target, attr);
            log.info(fileName+"-视频文件压缩总耗时：" + (System.currentTimeMillis() - time)/1000);
            return target;
        } catch (Exception e) {
            log.info("---error--"+fileName+"压缩异常："+e.getMessage());
        }finally {//压缩是否成功，都要把生成的临时文件删除，避免占用服务器内存
            if(source.length()>0 && source.delete()){
                log.info("源文件已删除！");
            }
        }
        return null;
    }

    public static File compressionTmpVideo(MultipartFile multipartFile, String fileName,String savePath){
        String targetFileName = fileName;
        if(fileName.indexOf('.')>-1){
            targetFileName = fileName.split("\\.")[0]+"-evt."+fileName.split("\\.")[1];
        }
        String allPath = savePath + File.separator + targetFileName;
        File file = new File(allPath);
        try(InputStream in = multipartFile.getInputStream();OutputStream out= new FileOutputStream(file)){
            long time = System.currentTimeMillis();
            byte[] bytes=new byte[1024];
            int len = 0;
            while((len=in.read(bytes))!=-1){
                out.write(bytes,0,len);
            }
            in.close();
            return file;
        }catch (Exception e){
            log.info("---error--"+"压缩视频文件，下载临时原视频文件到服务器端异常："+e.getMessage());
        }
        return null;
    }

    public static boolean checkFileSize(Long len, int size, String unit) {
        double fileSize = 0;
        if ("B".equals(unit.toUpperCase())) {
            fileSize = (double) len;
        } else if ("K".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1024;
        } else if ("M".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1048576;
        } else if ("G".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1073741824;
        }
        if (fileSize > size) {
            return true;
        }
        return false;
    }

}
