#这里填写阿语翻译
MSG.sysres.common.uploadSuccess=تم التحميل بنجاح!
ERR.sysres.common.uploadFail=فشل في التحميل!
MSG.sysres.common.copySuccess=تم النسخ بنجاح
MSG.sysres.common.msgSendSuccess=تم إرسال الرسالة بنجاح
MSG.sysres.websocket.setSender=يرجى تحديد المرسل!
MSG.sysres.pdfFileImpl.localTitle=مصدر البيانات الإحصائية: أمثلة مستخدمة في ((نظام EP-السجل الموحد-تحليل أداء الواجهة))
ERR.sysres.tableService.getPageFail=فشل في تنفيذ الحصول على البيانات على الصفحات doExport:
MSG.sysres.tableService.serviceNotFound=لم يتم العثور على خدمة %s.
MSG.sysres.tableService.enterDownload=يرجى النقر فوق %s لتنزيل الملف.
MSG.sysres.tableService.exportFinish=اكتمل التصدير
####################=
ERR.sysres.WebSocketDto.msgNull=لا يمكن أن تكون رسالة الإرسال فارغة
ERR.sysres.AttachCopyBillVO.originTicketIdNull=لا يمكن أن يكون معرف المسند الأصلي فارغًا
ERR.sysres.AttachCopyBillVO.newTicketIdNull=لا يمكن أن يكون معرف المسند الجديد فارغًا
MSG.sysres.WebSocketCServer.mqSendFail=فشل في توزيع رسالة MQ.
MSG.sysres.WebSocketCServer.heartbeatResponse=استجابة ضربات القلب
MSG.sysres.PdfFileImpl.commonLogCount=مصدر البيانات الإحصائية: أمثلة مستخدمة في ((نظام EP-السجل الموحد-تحليل أداء الواجهة))
MSG.sysres.PdfFileImpl.createBy=تم إنشاؤه بواسطة %s
MSG.sysres.PdfFileImpl.checker=الفاحص
MSG.sysres.PdfFileImpl.checkTime=وقت الفحص
MSG.sysres.PdfFileImpl.countPeriod=فترة الإحصاء
MSG.sysres.PdfFileImpl.systemEnv=بيئة النظام
MSG.sysres.PdfFileImpl.performanceCheckList=1. قائمة فحص أداء الواجهة
MSG.sysres.PdfFileImpl.averageCostTimeTop=قائمة أعلى عشر لمتوسط الوقت المستغرق
MSG.sysres.PdfFileImpl.serverName=اسم الخدمة
MSG.sysres.PdfFileImpl.interfaceUri=URI للواجهة
MSG.sysres.PdfFileImpl.successRate=معدل النجاح
MSG.sysres.PdfFileImpl.averageCostTime=متوسط الوقت المستغرق
MSG.sysres.PdfFileImpl.runTimes=عدد مرات التنفيذ (مرة)
MSG.sysres.PdfFileImpl.handler=المعالج
MSG.sysres.PdfFileImpl.handleTime=وقت المعالجة
MSG.sysres.PdfFileImpl.remarks=الملاحظة
MSG.sysres.PdfFileImpl.costTimeTop=قائمة أعلى عشر للوقت المستغرق
MSG.sysres.PdfFileImpl.costTime=وقت التنفيذ
MSG.sysres.PdfFileImpl.costTimems=الوقت المستغرق للتنفيذ (مللي ثانية)
MSG.sysres.PdfFileImpl.exceptionTop=1.3 أعلى عشر في الاستثناء في الخدمة
MSG.sysres.PdfFileImpl.exceptionInfo=معلومات الاستثناء
MSG.sysres.PdfFileImpl.exceptionAccount=عدد الاستثناءات
MSG.sysres.PdfFileImpl.searchHelpPerformanceList=2. قائمة فحص أداء المساعدة في البحث
MSG.sysres.PdfFileImpl.searchHelpName=اسم المساعدة في البحث
MSG.sysres.PdfFileImpl.databasePerformanceList=3. قائمة فحص أداء تنفيذ قاعدة البيانات
MSG.sysres.PdfFileImpl.showSqlTop=3.1 قائمة أعلى عشر لـSql البطيء
MSG.sysres.PdfFileImpl.showSqlCount=مصدر البيانات الإحصائية: سجل SQl البطيء في قاعدة البيانات، أمثلة مستخدمة في ((نظام EP-السجل الموحد-سجل SQL البطيء))
MSG.sysres.PdfFileImpl.clientIp=معرف العميل
MSG.sysres.PdfFileImpl.databaseAddress=عنوان قاعدة البيانات
MSG.sysres.PdfFileImpl.databaseInstance=مثيل قاعدة البيانات
MSG.sysres.PdfFileImpl.returnRecordCount=عدد سجلات العودة
MSG.sysres.PdfFileImpl.quartzTask=4. مهام التوقيت
MSG.sysres.PdfFileImpl.exceptionCount=4.1 قائمة أعلى عشر لإحصاءات الاستثناء
MSG.sysres.PdfFileImpl.quartzLogCount=مصدر البيانات الإحصائية: ((نظام EP-السجل الموحد-سجل مهام التوقيت))
MSG.sysres.PdfFileImpl.name=الاسم
MSG.sysres.PdfFileImpl.failTimes=عدد مرات الفشل
MSG.sysres.PdfFileImpl.execCountTop=4.2 قائمة أعلى عشر لإحصاءات التنفيذ
MSG.sysres.PdfFileImpl.execTimes=عدد مرات التنفيذ
MSG.sysres.MyHeaderFooter.pageStartEnd=رأس الصفحة/ تذييل الصفحة
MSG.sysres.MyHeaderFooter.currentPage=الصفحة الـ%s/
MSG.sysres.MyHeaderFooter.totalPage=%s صفحة إجماليًا
#############################=
ERR.message.MessageModuleEnum.mobile=رقم الهاتف المحمول
ERR.message.MessageModuleEnum.emailAddress=عنوان البريد الالكتروني
ERR.message.MessageModuleEnum.wxAccount=حساب WeChat
ERR.message.MessageModuleEnum.dingAccount=حساب DingTalk
ERR.message.DingTemplateDTO.messageTitleNull=لا يمكن أن يكون عنوان الرسالة فارغًا!
ERR.message.DingTemplateDTO.messageContentNull=لا يمكن أن يكون محتوى الرسالة فارغًا!
ERR.message.DingTemplateDTO.redirectUrlNull=لا يمكن أن يكون URL للانتقال فارغًا!
ERR.message.DingTemplateDTO.methodContent202=محتوى بوسيلة 202
ERR.message.MessageTemplateSendDTO.publishDateNull=لا يمكن أن يكون تاريخ الإصدار فارغًا
ERR.message.MessageTemplateSendDTO.objectDetailNull=لا يمكن أن تكون تفاصيل الكائن فارغة
ERR.message.MessageTemplateSendUserDTO.busiSysCodeNull=لا يمكن أن يكون رقم نظام الأعمال فارغًا
ERR.message.SendDingDTO.dingIdNull=لا يمكن أن يكون معرف مستخدم DingTalk فارغًا!
ERR.message.SendDingEPDTO.epsysIdNull=لا يمكن أن يكون معرف مستخدم نظام EP فارغًا!
ERR.message.SendDingEPDTO.epsysCompanyIdNull=معرف الشركة الذي ينتمي إليه معرف مستخدم نظام EP!
ERR.message.SendMailDTO.emailReceiverNull=لا يمكن أن يكون عنوان مستلم البريد الإلكتروني فارغًا!
ERR.message.SendSmsDTO.smsReceiverNull=لا يمكن أن يكون عنوان مستلم الرسالة القصيرة فارغًا!
ERR.message.SendSmsDTO.templateCodeNull=لا يمكن أن يكون رقم القالب فارغًا!
ERR.message.SendWxTemplateDTO.wxReceiverAddressNull=لا يمكن أن يكون عنوان مستلم WeChat فارغًا!
ERR.message.SendWxTemplateDTO.templateContentNull=لا يمكن أن يكون محتوى القالب فارغًا!
ERR.message.VerificationDTO.mobileNull=لا يمكن أن يكون رقم الهاتف المحمول فارغًا!
ERR.message.MessageFactory.messageFactoryNoTemplate=القالب غير المعرف لمصنع الرسائل:


