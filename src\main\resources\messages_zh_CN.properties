#这里填写中文翻译
MSG.sysres.common.uploadSuccess=上传成功！
ERR.sysres.common.uploadFail=上传失败！
MSG.sysres.common.copySuccess=copy成功
MSG.sysres.common.msgSendSuccess=消息发送成功
MSG.sysres.websocket.setSender=请指定发送者！
MSG.sysres.pdfFileImpl.localTitle=统计数据来源:《EP系统-统一日志-接口性能分析》用例
ERR.sysres.tableService.getPageFail=执行doExport 分页获取数据失败：
MSG.sysres.tableService.serviceNotFound=未发现%s服务。
MSG.sysres.tableService.enterDownload=请点击 %s 下载文件。
MSG.sysres.tableService.exportFinish=导出完成
##################################
ERR.sysres.WebSocketDto.msgNull=发送消息不可为空
ERR.sysres.AttachCopyBillVO.originTicketIdNull=原单据id不可以为空
ERR.sysres.AttachCopyBillVO.newTicketIdNull=新单据id不可以为空
MSG.sysres.WebSocketCServer.mqSendFail=MQ消息分发失败。
MSG.sysres.WebSocketCServer.heartbeatResponse=心跳响应
MSG.sysres.PdfFileImpl.commonLogCount=统计数据来源:《EP系统-统一日志-接口性能分析》用例
MSG.sysres.PdfFileImpl.createBy=由%s创建
MSG.sysres.PdfFileImpl.checker=点检人
MSG.sysres.PdfFileImpl.checkTime=点检时间
MSG.sysres.PdfFileImpl.countPeriod=统计周期
MSG.sysres.PdfFileImpl.systemEnv=系统环境
MSG.sysres.PdfFileImpl.performanceCheckList=1、接口性能点检清单
MSG.sysres.PdfFileImpl.averageCostTimeTop=平均耗时Top10
MSG.sysres.PdfFileImpl.serverName=服务名
MSG.sysres.PdfFileImpl.interfaceUri=接口URI
MSG.sysres.PdfFileImpl.successRate=成功率
MSG.sysres.PdfFileImpl.averageCostTime=平均耗时(ms)
MSG.sysres.PdfFileImpl.runTimes=执行数量(次)
MSG.sysres.PdfFileImpl.handler=处理人
MSG.sysres.PdfFileImpl.handleTime=处理时间
MSG.sysres.PdfFileImpl.remarks=备注
MSG.sysres.PdfFileImpl.costTimeTop=耗时Top10
MSG.sysres.PdfFileImpl.costTime=执行时间
MSG.sysres.PdfFileImpl.costTimems=执行耗时(ms)
MSG.sysres.PdfFileImpl.exceptionTop=1.3、服务异常Top10
MSG.sysres.PdfFileImpl.exceptionInfo=异常信息
MSG.sysres.PdfFileImpl.exceptionAccount=异常数量
MSG.sysres.PdfFileImpl.searchHelpPerformanceList=2、搜索帮助性能点检清单
MSG.sysres.PdfFileImpl.searchHelpName=搜索帮助名称
MSG.sysres.PdfFileImpl.databasePerformanceList=3、数据库执行性能点检清单
MSG.sysres.PdfFileImpl.showSqlTop=3.1、慢Sql Top10
MSG.sysres.PdfFileImpl.showSqlCount=统计数据来源:数据库慢SQl日志,《EP系统-统一日志-慢SQL日志》用例
MSG.sysres.PdfFileImpl.clientIp=客户端IP
MSG.sysres.PdfFileImpl.databaseAddress=数据库地址
MSG.sysres.PdfFileImpl.databaseInstance=数据库实例
MSG.sysres.PdfFileImpl.returnRecordCount=返回记录数
MSG.sysres.PdfFileImpl.quartzTask=4、定时任务
MSG.sysres.PdfFileImpl.exceptionCount=4.1、异常统计Top10
MSG.sysres.PdfFileImpl.quartzLogCount=统计数据来源:《EP系统-统一日志-定时任务日志》
MSG.sysres.PdfFileImpl.name=名称
MSG.sysres.PdfFileImpl.failTimes=失败次数
MSG.sysres.PdfFileImpl.execCountTop=4.2、执行统计Top10
MSG.sysres.PdfFileImpl.execTimes=执行次数
MSG.sysres.MyHeaderFooter.pageStartEnd=我是页眉/页脚
MSG.sysres.MyHeaderFooter.currentPage=第%s页/
MSG.sysres.MyHeaderFooter.totalPage=总%s页
#############################=
ERR.message.MessageModuleEnum.mobile=手机号
ERR.message.MessageModuleEnum.emailAddress=邮箱地址
ERR.message.MessageModuleEnum.wxAccount=微信号
ERR.message.MessageModuleEnum.dingAccount=钉钉号
ERR.message.DingTemplateDTO.messageTitleNull=消息标题不能为空！
ERR.message.DingTemplateDTO.messageContentNull=消息内容不能为空！
ERR.message.DingTemplateDTO.redirectUrlNull=跳转URL不能为空！
ERR.message.DingTemplateDTO.methodContent202=202方式的内容！
ERR.message.MessageTemplateSendDTO.publishDateNull=发布日期不能为空
ERR.message.MessageTemplateSendDTO.objectDetailNull=对象明细不能为空
ERR.message.MessageTemplateSendUserDTO.busiSysCodeNull=业务系统编号不能为空
ERR.message.SendDingDTO.dingIdNull=钉钉用户id不能为空！
ERR.message.SendDingEPDTO.epsysIdNull=EP系统用户id不能为空！
ERR.message.SendDingEPDTO.epsysCompanyIdNull=EP系统用户id所属公司ID！
ERR.message.SendMailDTO.emailReceiverNull=邮件接收方地址不能为空！
ERR.message.SendSmsDTO.smsReceiverNull=短信接收方地址不能为空！
ERR.message.SendSmsDTO.templateCodeNull=模板编号不能为空！
ERR.message.SendWxTemplateDTO.wxReceiverAddressNull=微信接收方地址不能为空！
ERR.message.SendWxTemplateDTO.templateContentNull=模板内容不能为空！
ERR.message.VerificationDTO.mobileNull=手机号码不能为空！
ERR.message.MessageFactory.messageFactoryNoTemplate=消息工厂未定义模板：

