package com.qm.ep.sysres.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 按业务代码和单据id返回该对应的文件
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-14
 */
@ApiModel(value = "AttachCenterFileVO对象", description = "按业务代码和单据id返回该对应的文件")
@Data
public class AttachCenterFileVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "附件ID")
    private String id;

    @ApiModelProperty(value = "哪个业务场景使用，公司+业务类型唯一")
    private String vbustype;

    @ApiModelProperty(value = "按公司设置，对应公司ID")
    private String nco;

    @ApiModelProperty(value = "对应单据ID")
    private String nbillid;

    @ApiModelProperty(value = "文件名")
    private String vfilename;

    @ApiModelProperty(value = "文件的扩展名")
    private String vextension;

    @ApiModelProperty(value = "附件对应的项目代码")
    private String vitem;

    @ApiModelProperty(value = "附件备注")
    private String vremark;

    @ApiModelProperty(value = "上传人员ID")
    private String nopr;

    @ApiModelProperty(value = "上传日期")
    private Date dup;

    @ApiModelProperty(value = "附件链接地址")
    private String vaddr;

    @ApiModelProperty(value = "文件流化后浏览器识别的类型")
    private String vcontenttype;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @ApiModelProperty(value = "操作员名称")
    private String vpersonname;

    @ApiModelProperty(value = "文件类型")
    private String vfiletype;

    @ApiModelProperty(value = "图标名称")
    private String vicon;

    @ApiModelProperty(value = "第三方附件地址")
    private String vitffulfilename;
}
