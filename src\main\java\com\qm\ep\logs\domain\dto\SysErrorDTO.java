package com.qm.ep.logs.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysErrorDTO extends JsonParamDto implements Serializable {
    private static final long serialVersionUID = 2073684414842102685L;

    /**
     * id
     */
    private String id;

    @JsonAlias("type")
    private String type;

    @JsonAlias("path")
    private String path;

    @JsonAlias("message")
    private String message;

    @JsonAlias("tsf_app_id")
    private String tsfAppId;

    @JsonAlias("tsf_trace_id")
    private String tsTraceId;

    @JsonAlias("tsf_span_id")
    private String tsfSpanId;

    @JsonAlias("tsf_span_export")
    private String tsfSpanExport;

    @JsonAlias("ep-logintimestamp")
    private String epLogintimestamp;

    @JsonAlias("ep-opr-id")
    private String epOprId;

    @JsonAlias("ep-opr-code")
    private String epOprCode;

    @JsonAlias("ep-opr-name")
    private String epOprName;

    @JsonAlias("ep-com-id")
    private String epComId;

    @JsonAlias("ep-tenant-id")
    private String epTenantId;

    @JsonAlias("log_msg")
    private String logMsg;

    @JsonAlias("log_thread")
    private String logThread;

    @JsonAlias("log_level")
    private String level;

    @JsonAlias("timestamp")
    private Date timestamp;

    @JsonAlias("log_class")
    private String logClass;

    @JsonAlias("host")
    private String host;

    @JsonAlias("log_msg_sub")
    private String[] logMsgSub;

    /**
     * request header信息
     */
    @JsonAlias("LOGIN_KEY")
    private String loginkey;

    /**
     * 访问时间 开始时间
     */
    private String startTime;

    /**
     * 访问时间 结束时间
     */
    private String endTime;
    /**
     * 访问时间 开始日期
     */
    private String startDateTime;

    /**
     * 访问时间 结束时间
     */
    private String endDateTime;

    /**
     * 命中数
     */
    private Integer limit;
}
