package com.qm.ep.logs.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "系统错误 DTO")
@Data
public class SysErrorDTO extends JsonParamDto implements Serializable {
    private static final long serialVersionUID = 2073684414842102685L;

    /**
     * id
     */
    @Schema(description = "主键")
    private String id;

    @Schema(description = "类型")
    @JsonAlias("type")
    private String type;

    @Schema(description = "路径")
    @JsonAlias("path")
    private String path;

    @Schema(description = "消息")
    @JsonAlias("message")
    private String message;

    @Schema(description = "应用程序 ID")
    @JsonAlias("tsf_app_id")
    private String tsfAppId;

    @Schema(description = "跟踪 ID")
    @JsonAlias("tsf_trace_id")
    private String tsTraceId;

    @Schema(description = "跨度 ID")
    @JsonAlias("tsf_span_id")
    private String tsfSpanId;

    @Schema(description = "导出")
    @JsonAlias("tsf_span_export")
    private String tsfSpanExport;

    @Schema(description = "登录时间戳（英语）")
    @JsonAlias("ep-logintimestamp")
    private String epLogintimestamp;

    @Schema(description = "操作人员ID")
    @JsonAlias("ep-opr-id")
    private String epOprId;

    @Schema(description = "操作人员编码")
    @JsonAlias("ep-opr-code")
    private String epOprCode;

    @Schema(description = "操作人员名称")
    @JsonAlias("ep-opr-name")
    private String epOprName;

    @Schema(description = "操作ID")
    @JsonAlias("ep-com-id")
    private String epComId;

    @Schema(description = "租户 ID")
    @JsonAlias("ep-tenant-id")
    private String epTenantId;

    @Schema(description = "日志消息")
    @JsonAlias("log_msg")
    private String logMsg;

    @Schema(description = "日志线程")
    @JsonAlias("log_thread")
    private String logThread;

    @Schema(description = "级别")
    @JsonAlias("log_level")
    private String level;

    @Schema(description = "时间戳")
    @JsonAlias("timestamp")
    private Date timestamp;

    @Schema(description = "数据Log 类")
    @JsonAlias("log_class")
    private String logClass;

    @Schema(description = "主机")
    @JsonAlias("host")
    private String host;

    @Schema(description = "日志 msg sub")
    @JsonAlias("log_msg_sub")
    private String[] logMsgSub;

    /**
     * request header信息
     */
    @Schema(description = "数据request header信息")
    @JsonAlias("LOGIN_KEY")
    private String loginkey;

    /**
     * 访问时间 开始时间
     */
    @Schema(description = "访问时间 开始时间")
    private String startTime;

    /**
     * 访问时间 结束时间
     */
    @Schema(description = "访问时间 结束时间")
    private String endTime;
    /**
     * 访问时间 开始日期
     */
    @Schema(description = "访问时间 开始日期")
    private String startDateTime;

    /**
     * 访问时间 结束时间
     */
    @Schema(description = "访问时间 结束时间")
    private String endDateTime;

    /**
     * 命中数
     */
    @Schema(description = "命中数")
    private Integer limit;
}
