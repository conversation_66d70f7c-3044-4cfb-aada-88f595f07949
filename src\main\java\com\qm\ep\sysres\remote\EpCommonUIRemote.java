package com.qm.ep.sysres.remote;

import com.qm.ep.sysres.domain.bean.SreTableExportLogDO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Repository
@FeignClient(name = "common-ui", fallbackFactory = EpCommonUIFactory.class)
public interface EpCommonUIRemote {

    /**
     * Excel导出日志
     *
     * @param tempDO Excel导出日志信息
     * @return 保存后的Excel导出日志
     */
    @PostMapping("/sreTableExportLog/save")
    JsonResultVo<SreTableExportLogDO> tableExportLogSave(@RequestBody SreTableExportLogDO tempDO);

}
