package com.qm.cloud.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * JDK17兼容性修复的HTTP请求过滤器
 * 解决"The body is not set"和连接关闭问题
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Component
@Slf4j
public class HttpRequestFilterJdk17Fix implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().pathWithinApplication().value();
        String method = request.getMethod().name();
        String uri = request.getURI().toString();
        
        log.info("---info---请求开始 - Method: {}, Path: {}, URI: {}", method, path, uri);
        
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        
        return chain.filter(exchange)
            // JDK17兼容性：添加超时处理
            .timeout(Duration.ofSeconds(getTimeoutForPath(path)))
            
            // JDK17兼容性：成功处理
            .doOnSuccess(result -> {
                long duration = System.currentTimeMillis() - startTime;
                log.info("---info---请求成功 - Path: {}, 耗时: {}ms", path, duration);
            })
            
            // JDK17兼容性：错误处理
            .doOnError(throwable -> {
                long duration = System.currentTimeMillis() - startTime;
                log.error("---error---请求失败 - Path: {}, 耗时: {}ms, 错误: {}", 
                    path, duration, throwable.getMessage(), throwable);
            })
            
            // JDK17兼容性：错误恢复机制
            .onErrorResume(throwable -> handleJdk17CompatibilityError(exchange, throwable, path));
    }

    /**
     * 处理JDK17兼容性错误
     */
    private Mono<Void> handleJdk17CompatibilityError(ServerWebExchange exchange, Throwable throwable, String path) {
        String errorMessage = throwable.getMessage();
        
        // 检查是否是JDK17兼容性相关的错误
        if (isJdk17CompatibilityError(throwable)) {
            log.warn("---warn---检测到JDK17兼容性问题 - Path: {}, 错误: {}", path, errorMessage);
            return handleJdk17Error(exchange, throwable);
        }
        
        // 检查是否是超时错误
        if (isTimeoutError(throwable)) {
            log.warn("---warn---检测到超时错误 - Path: {}, 错误: {}", path, errorMessage);
            return handleTimeoutError(exchange, throwable);
        }
        
        // 检查是否是连接错误
        if (isConnectionError(throwable)) {
            log.warn("---warn---检测到连接错误 - Path: {}, 错误: {}", path, errorMessage);
            return handleConnectionError(exchange, throwable);
        }
        
        // 其他错误，返回通用错误响应
        return handleGenericError(exchange, throwable);
    }

    /**
     * 判断是否是JDK17兼容性错误
     */
    private boolean isJdk17CompatibilityError(Throwable throwable) {
        String message = throwable.getMessage();
        return message != null && (
            message.contains("The body is not set") ||
            message.contains("Did handling complete with success") ||
            message.contains("insufficient data written") ||
            message.contains("Connection has been closed")
        );
    }

    /**
     * 判断是否是超时错误
     */
    private boolean isTimeoutError(Throwable throwable) {
        return throwable instanceof java.util.concurrent.TimeoutException ||
               throwable.getMessage().contains("timeout") ||
               throwable.getMessage().contains("Timeout");
    }

    /**
     * 判断是否是连接错误
     */
    private boolean isConnectionError(Throwable throwable) {
        String message = throwable.getMessage();
        return message != null && (
            message.contains("Connection refused") ||
            message.contains("Connection reset") ||
            message.contains("No route to host") ||
            message.contains("Connection timed out")
        );
    }

    /**
     * 处理JDK17兼容性错误
     */
    private Mono<Void> handleJdk17Error(ServerWebExchange exchange, Throwable throwable) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.BAD_GATEWAY);
        
        String errorBody = String.format(
            "{\"error\":\"JDK17 compatibility issue\",\"code\":502,\"message\":\"%s\",\"timestamp\":\"%s\"}",
            throwable.getMessage().replace("\"", "\\\""),
            java.time.Instant.now().toString()
        );
        
        return writeErrorResponse(response, errorBody);
    }

    /**
     * 处理超时错误
     */
    private Mono<Void> handleTimeoutError(ServerWebExchange exchange, Throwable throwable) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.GATEWAY_TIMEOUT);
        
        String errorBody = String.format(
            "{\"error\":\"Request timeout\",\"code\":504,\"message\":\"%s\",\"timestamp\":\"%s\"}",
            "请求超时，请稍后重试或联系管理员",
            java.time.Instant.now().toString()
        );
        
        return writeErrorResponse(response, errorBody);
    }

    /**
     * 处理连接错误
     */
    private Mono<Void> handleConnectionError(ServerWebExchange exchange, Throwable throwable) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
        
        String errorBody = String.format(
            "{\"error\":\"Service unavailable\",\"code\":503,\"message\":\"%s\",\"timestamp\":\"%s\"}",
            "服务暂时不可用，请稍后重试",
            java.time.Instant.now().toString()
        );
        
        return writeErrorResponse(response, errorBody);
    }

    /**
     * 处理通用错误
     */
    private Mono<Void> handleGenericError(ServerWebExchange exchange, Throwable throwable) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
        
        String errorBody = String.format(
            "{\"error\":\"Internal server error\",\"code\":500,\"message\":\"%s\",\"timestamp\":\"%s\"}",
            "服务器内部错误",
            java.time.Instant.now().toString()
        );
        
        return writeErrorResponse(response, errorBody);
    }

    /**
     * 写入错误响应
     */
    private Mono<Void> writeErrorResponse(ServerHttpResponse response, String errorBody) {
        DataBuffer buffer = response.bufferFactory().wrap(errorBody.getBytes(StandardCharsets.UTF_8));
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.getHeaders().add("Cache-Control", "no-cache");
        
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 根据路径确定超时时间
     */
    private int getTimeoutForPath(String path) {
        // 导出相关接口使用更长的超时时间
        if (path.contains("export") || path.contains("Export") || 
            path.contains("searchInvocie") || path.contains("table")) {
            return 600; // 10分钟
        }
        
        // 查询接口使用中等超时时间
        if (path.contains("search") || path.contains("query") || path.contains("list")) {
            return 300; // 5分钟
        }
        
        // 其他接口使用默认超时时间
        return 120; // 2分钟
    }

    @Override
    public int getOrder() {
        return -1; // 确保在其他过滤器之前执行
    }
}
