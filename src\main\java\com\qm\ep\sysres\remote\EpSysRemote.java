package com.qm.ep.sysres.remote;

import com.qm.ep.sys.domain.dto.AttachImputItemDTO;
import com.qm.ep.sysres.domain.vo.AttachFileCenterVO;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * EP系统SYS模块下的的远程服务
 */
@Repository
@FeignClient(name = "tds-service-sys", fallbackFactory = EpSysRemoteFactory.class)
public interface EpSysRemote {

    @PostMapping("/attachFile/getAttachByBustype")
    @ApiOperation(value = "根据业务代码和单据id获取文件列表信息")
    JsonResultVo<AttachFileCenterVO> getAttachByBustype(@RequestHeader("tenantId") String tenantId, @RequestBody AttachImputItemDTO attachImputItemDTO);

    @PostMapping("/user/getPersonIdByCode")
    @ApiOperation(value = "根据业务代码和单据id获取文件列表信息")
    JsonResultVo<String> getPersonIdByCode(@RequestBody String personCode);

}
