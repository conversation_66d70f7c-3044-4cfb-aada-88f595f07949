<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.sysres.mapper.Sysc080Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.sysres.domain.bean.Sysc080DO">
        <id column="ID" property="id"/>
        <result column="VBUSTYPE" property="vbustype"/>
        <result column="VBUSTYPEDESC" property="vbustypedesc"/>
        <result column="VTABLE" property="vtable"/>
        <result column="VMODULE" property="vmodule"/>
        <result column="VMNTNMODE" property="vmntnmode"/>
        <result column="VISITEM" property="visitem"/>
        <result column="VDICTCODE" property="vdictcode"/>
        <result column="VWATERMARK" property="vwatermark"/>
        <result column="NCO" property="nco"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, VBUSTYPE, VBUSTYPEDESC, VTABLE, VMODULE, VMNTNMODE, VISITEM, VDICTCODE, NCO, VWATERMARK, DTSTAMP
    </sql>
    <select id="selectOneByBusType" resultMap="BaseResultMap">
        select  *  from  sysc080 where  VBUSTYPE = #{vbustype} limit 1
    </select>


</mapper>
