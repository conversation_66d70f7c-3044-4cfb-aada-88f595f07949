package com.qm.ep.sysres.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * JDK17服务器配置优化
 * 专门解决JDK17环境下的HTTP服务器问题
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Configuration
@Slf4j
public class Jdk17ServerConfig implements WebMvcConfigurer {

    /**
     * JDK17优化的Tomcat服务器配置
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> jdk17TomcatCustomizer() {
        return factory -> {
            log.info("启用JDK17优化的Tomcat服务器配置");
            
            factory.addConnectorCustomizers(this::customizeConnectorForJdk17);
            
            // JDK17兼容性：设置服务器属性
            factory.setPort(8339);
            factory.setContextPath("");
            
            log.info("JDK17 Tomcat服务器配置完成");
        };
    }

    /**
     * 自定义连接器配置以优化JDK17性能
     */
    private void customizeConnectorForJdk17(Connector connector) {
        log.info("配置Tomcat连接器以优化JDK17性能");
        
        // JDK17兼容性：基本连接配置
        connector.setProperty("connectionTimeout", "60000");        // 连接超时60秒
        connector.setProperty("keepAliveTimeout", "60000");         // Keep-Alive超时60秒
        connector.setProperty("maxKeepAliveRequests", "100");       // 最大Keep-Alive请求数
        
        // JDK17兼容性：线程池优化
        connector.setProperty("maxThreads", "300");                // 最大线程数
        connector.setProperty("minSpareThreads", "50");            // 最小空闲线程数
        connector.setProperty("acceptCount", "200");               // 接受连接数
        
        // JDK17兼容性：网络优化
        connector.setProperty("tcpNoDelay", "true");               // 启用TCP_NODELAY
        connector.setProperty("socketBuffer", "65536");            // 增大Socket缓冲区到64KB
        connector.setProperty("processorCache", "200");            // 处理器缓存
        
        // JDK17兼容性：HTTP协议优化
        connector.setProperty("maxHttpHeaderSize", "32768");       // 增大HTTP头大小到32KB
        connector.setProperty("maxPostSize", "104857600");         // 最大POST大小(100MB)
        connector.setProperty("maxSavePostSize", "104857600");     // 最大保存POST大小
        
        // JDK17兼容性：连接管理优化
        connector.setProperty("connectionLinger", "0");            // 连接延迟关闭时间
        connector.setProperty("socket.soReuseAddress", "true");    // 重用地址
        connector.setProperty("socket.soKeepAlive", "true");       // 保持连接活跃
        connector.setProperty("socket.soTimeout", "60000");        // Socket超时
        
        // JDK17兼容性：性能优化
        connector.setProperty("enableLookups", "false");           // 禁用DNS查找
        connector.setProperty("URIEncoding", "UTF-8");             // URI编码
        connector.setProperty("useBodyEncodingForURI", "true");    // 使用Body编码
        
        // JDK17兼容性：压缩配置（谨慎启用）
        connector.setProperty("compression", "on");                // 启用压缩
        connector.setProperty("compressionMinSize", "2048");       // 压缩最小大小2KB
        connector.setProperty("compressableMimeType", 
            "text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml");
        
        // JDK17兼容性：协议版本（支持HTTP/1.1和HTTP/2）
        connector.setProperty("protocol", "org.apache.coyote.http11.Http11NioProtocol");
        
        // JDK17兼容性：NIO优化
        connector.setProperty("pollerThreadCount", "2");           // Poller线程数
        connector.setProperty("selectorTimeout", "1000");          // 选择器超时
        
        log.info("JDK17 Tomcat连接器配置完成");
        log.info("连接超时: {}ms, Keep-Alive超时: {}ms, 最大线程数: {}, Socket缓冲区: {}bytes", 
            connector.getProperty("connectionTimeout"),
            connector.getProperty("keepAliveTimeout"),
            connector.getProperty("maxThreads"),
            connector.getProperty("socketBuffer"));
    }
}
