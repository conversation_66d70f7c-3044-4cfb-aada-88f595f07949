package com.qm.ep.sysres.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 按业务单据记录对应附件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Schema(title = "按业务单据记录对应附件信息", description = "按业务单据记录对应附件信息")
@Data
public class AttachFileDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;
    @Schema(title = "附件ID", description = "附件ID")
    private String id;
    @Schema(title = "哪个业务场景使用，公司+业务类型唯一", description = "哪个业务场景使用，公司+业务类型唯一")
    private String vbustype;
    @Schema(title = "按公司设置，对应公司ID", description = "按公司设置，对应公司ID")
    private String nco;
    @Schema(title = "对应单据ID", description = "对应单据ID")
    private String nbillid;
    @Schema(title = "文件名", description = "文件名")
    private String vfilename;
    @Schema(title = "文件的扩展名", description = "文件的扩展名")
    private String vextension;
    @Schema(title = "附件对应的项目代码", description = "附件对应的项目代码")
    private String vitem;
    @Schema(title = "附件备注", description = "附件备注")
    private String vremark;
    @Schema(title = "上传人员ID", description = "上传人员ID")
    private String nopr;
    @Schema(title = "上传日期", description = "上传日期")
    private Date dup;
    @Schema(title = "附件链接地址", description = "附件链接地址")
    private String vaddr;
    @Schema(title = "文件流化后浏览器识别的类型", description = "文件流化后浏览器识别的类型")
    private String vcontenttype;
    @Schema(title = "时间戳", description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
}