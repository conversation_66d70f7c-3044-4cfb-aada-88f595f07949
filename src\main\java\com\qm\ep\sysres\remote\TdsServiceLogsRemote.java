package com.qm.ep.sysres.remote;

import com.qm.ep.logs.domain.dto.*;
import com.qm.ep.logs.domain.es.GateWayLog;
import com.qm.ep.logs.domain.es.SlowSqlLog;
import com.qm.ep.logs.domain.vo.SearchVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/21
 */
@Repository
@FeignClient(name = "tds-service-logs", fallbackFactory = TdsServiceLogsRemoteFactory.class)
public interface TdsServiceLogsRemote {

    @Operation(summary = "接口性能分析", description = "接口性能分析[author:10027705]")
    @PostMapping("/gatewayLogs/timeConsumingAnalysis")
    JsonResultVo<List<Map<String, String>>> timeConsumingAnalysis(@RequestBody AnalyseDto analyseDto);

    @Operation(summary = "查询网关日志", description = "查询网关日志[author:10027705]")
    @PostMapping("/gatewayLogs/query")
    JsonResultVo<QmPage<GateWayLog>> save(@RequestBody GateWayLogDTO gateWayLogDTO);

    @Operation(summary = "异常分析", description = "异常分析[author:10027705]")
    @PostMapping("/SysError/timeConsumingAnalysis")
    JsonResultVo<List<Map<String, String>>> timeConsumingAnalysis(@RequestBody SysErrorDTO sysErrorDTO);

    @Operation(summary = "接口性能分析", description = "接口性能分析[author:10027705]")
    @PostMapping("/gatewayLogs/searchHelpAnalysis")
    JsonResultVo<List<Map<String, String>>> searchHelpAnalysis(@RequestBody SearchHelpAnalyseDto searchHelpAnalyseDto);

    @Operation(summary = "查询网关日志", description = "查询网关日志[author:10027705]")
    @PostMapping("/gatewayLogs/searchQueryTime")
    JsonResultVo<QmPage<SearchVO>> searchQueryTime(@RequestBody GateWayLogDTO gateWayLogDTO);

    @Operation(summary = "查询慢sql日志", description = "查询慢sql日志[author:10027705]")
    @PostMapping("/SlowSqlLog/query")
    JsonResultVo<QmPage<SlowSqlLog>> query(@RequestBody SlowSqlLogDTO slowSqlLogDTO);

    @PostMapping("/user/offlineByUser")
    JsonResultVo offlineByUser(@RequestHeader("companyid") String companyid,
                               @RequestHeader("userid") String operatorid,
                               @RequestHeader("username") String operatorname,
                               @RequestHeader("personcode") String personcode, @RequestParam String userId);
}
