package com.qm.ep.sysres.mapper;


import com.qm.ep.sysres.domain.bean.AttachFileDO;
import com.qm.ep.sysres.domain.dto.AttachFileDTO;
import com.qm.ep.sysres.domain.vo.AttachCenterFileVO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;

import java.util.List;

/**
 * <p>
 * 按业务单据记录对应附件信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
public interface AttachFileMapper extends QmBaseMapper<AttachFileDO> {

    /**
     * @description: 根据传入的业务类型和单据id获取附件列表
     * @author: liuyong
     * @time: 2020/11/14 12:19
     */
    List<AttachCenterFileVO> getAttachCenterFile(AttachFileDTO attachFileDTO);

    List<AttachCenterFileVO> getfilelist(String id);
    List<AttachCenterFileVO> getfilelistByBillid(String id);
    List<AttachCenterFileVO> getAttachCenterFileByAgent(AttachFileDTO attachFileDTO);

}
