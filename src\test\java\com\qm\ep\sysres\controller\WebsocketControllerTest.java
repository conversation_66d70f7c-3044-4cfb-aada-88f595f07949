package com.qm.ep.sysres.controller;

import com.qm.ep.sysres.domain.dto.WebSocketDto;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.controller.BaseTestController;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2021/2/3$ 8:09$
 **/
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class WebsocketControllerTest extends BaseTestController<WebsocketController> {
    /**
     * 每个TestCase函数执行前
     */
    @Before
    public void beforMethod() {
        initUser(UserConstants.USER_CODE_COMPANY);
    }

    /**
     * 覆盖dto
     */
    @Test
    public void moduleDtoTest() {
        EqualsVerifier.simple().forClass(WebSocketDto.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    @Test
    public void getThisOnlineNumber() {
        JsonResultVo<Long> result = testController.getThisOnlineNumber();
        assertJsonResultVo(result);
    }

    @Test
    public void sendMessage() {
        WebSocketDto socket = new WebSocketDto();
        socket.setSendType(0);
        try {
            JsonResultVo<String> result = testController.sendMessage(socket);
            assertJsonResultVo(result);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        }
    }

    @Test
    public void send() {
        WebSocketDto socket = new WebSocketDto();
        socket.setSendType(0);
        try {
            JsonResultVo<String> result = testController.send(socket);
            assertJsonResultVo(result);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        }
    }
}
