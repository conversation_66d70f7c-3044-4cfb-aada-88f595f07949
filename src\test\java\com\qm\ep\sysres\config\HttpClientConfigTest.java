package com.qm.ep.sysres.config;

import org.apache.hc.client5.http.classic.HttpClient;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HTTP客户端配置测试
 * 验证JDK17兼容性修复
 */
@SpringBootTest
@ActiveProfiles("local")
class HttpClientConfigTest {

    @Test
    void testHttpClientConfigCreation() {
        HttpClientConfig config = new HttpClientConfig();
        
        // 测试默认配置值
        assertEquals(2700, config.getMaxTotal());
        assertEquals(100, config.getDefaultMaxPerRoute());
        assertEquals(30, config.getConnectTimeout());
        assertEquals(30, config.getConnectionRequestTimeout());
        assertEquals(300, config.getResponseTimeout());
        assertEquals(3, config.getRetryCount());
        assertEquals(1, config.getRetryInterval());
        assertEquals(5, config.getKeepAliveTime());
    }

    @Test
    void testJdk17CompatibleHttpClientFactory() {
        HttpClientConfig config = new HttpClientConfig();
        ClientHttpRequestFactory factory = config.createJdk17CompatibleHttpClientFactory();
        
        assertNotNull(factory);
        assertTrue(factory instanceof HttpComponentsClientHttpRequestFactory);
        
        HttpComponentsClientHttpRequestFactory httpFactory = (HttpComponentsClientHttpRequestFactory) factory;
        // 验证工厂配置正确
        assertNotNull(httpFactory);
    }

    @Test
    void testConfigurationProperties() {
        HttpClientConfig config = new HttpClientConfig();
        
        // 测试设置自定义值
        config.setMaxTotal(5000);
        config.setDefaultMaxPerRoute(200);
        config.setConnectTimeout(60);
        config.setResponseTimeout(600);
        
        assertEquals(5000, config.getMaxTotal());
        assertEquals(200, config.getDefaultMaxPerRoute());
        assertEquals(60, config.getConnectTimeout());
        assertEquals(600, config.getResponseTimeout());
    }
}
