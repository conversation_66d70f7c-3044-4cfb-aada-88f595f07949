package com.qm.ep.sysres.remote;

import com.qm.ep.sysres.domain.dto.MessageSendNoticeDTO;
import com.qm.ep.sysres.domain.po.MessageNoticePO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;

public class NoticeRemoteHystrix  extends QmRemoteHystrix<NoticeRemote> implements NoticeRemote{
    @Override
    public JsonResultVo<String> getIdWorkerSTR(@RequestBody @Valid String vModelName){
        return getResult();
    }

    @Override
    public JsonResultVo<MessageNoticePO> saveNoticeByOtherUse(@RequestBody MessageSendNoticeDTO messageSendNoticeDTO){
        return getResult();
    }
}
