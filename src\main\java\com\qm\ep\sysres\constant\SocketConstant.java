package com.qm.ep.sysres.constant;

/**
 * <AUTHOR>
 * @Date 2021/1/28$ 13:49$
 **/
public class SocketConstant {
    /**
     * 这个类不能实例化
     */
    private SocketConstant() {
    }

    /**
     * Redis中Key的前缀
     */
    public static final String REDIS_KEY_PRE = "Socket";

    // 在线人数
    public static final String SOCKET_ONLINE_NUMBER = "SOCKET_ONLINE_NUMBER";
    // 在线用户
    public static final String SOCKET_USER = "SOCKET_USER";
    // MQ的交换机
    public static final String WEBSOCKET_EXCHANGE = "qm.base.websocket.exchange";
    // MQ的队列前缀
    public static final String QUEUE_NAME_WEBSOCKET = "websocket-queue";

    public static final String RABBIT_DEADLETTER_EXCHANGE = "x-dead-letter-exchange";
    public static final String RABBIT_DEADLETTER_ROUTINGKEY = "x-dead-letter-routing-key";
    public static final String RABBIT_SOCKET_DEADLETTER_EXCHANGE = "qm-socket-deadletter-exchange";
    public static final String RABBIT_SOCKET_DEADLETTER_ROUTINGKEY = "qm-socket-deadletter-routingkey";
    public static final String RABBIT_SOCKET_DEADLETTER_QUEUE = "qm-socket-deadletter-queue";

    /**
     * 消息json key:cmd
     */
    public static final String MSG_CMD = "cmd";

    /**
     * 消息json key:msgId
     */
    public static final String MSG_ID = "msgId";

    /**
     * 消息json key:msgTxt
     */
    public static final String MSG_TXT = "msgTxt";

    /**
     * 消息json key:userId
     */
    public static final String MSG_USER_ID = "userId";

    /**
     * 消息类型 heartcheck
     */
    public static final String CMD_CHECK = "heartcheck";

    /**
     * 消息类型 user 用户消息
     */
    public static final String CMD_USER = "user";

    /**
     * 消息类型 topic 系统通知
     */
    public static final String CMD_TOPIC = "topic";

    /**
     * 消息类型 email
     */
    public static final String CMD_EMAIL = "email";

    /**
     * 消息类型 meetingsign 会议签到
     */
    public static final String CMD_SIGN = "sign";

    /**
     * 消息类型 新闻发布/取消
     */
    public static final String NEWS_PUBLISH = "publish";

    /**
     *  app端推送会话标识后缀
     */
    public static final String APP_SESSION_SUFFIX = "_app";    //app端推送会话标识后缀

}
