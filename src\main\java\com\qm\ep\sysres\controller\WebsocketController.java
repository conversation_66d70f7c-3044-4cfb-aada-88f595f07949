package com.qm.ep.sysres.controller;

import com.qm.ep.sysres.domain.dto.SendInstantMessageDTO;
import com.qm.ep.sysres.domain.dto.WebSocketDto;
import com.qm.ep.sysres.service.WebSocketService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/1/28$ 15:47$
 **/
@Api(tags = "WebSocket消息收发")
@RestController
@RequestMapping("/websocket")
public class WebsocketController extends BaseController {
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private WebSocketService webSocketService;

    private static final String SYSRES_COMMON_MSGSENDSUCCESS= "MSG.sysres.common.msgSendSuccess";

    /**
     * 直接发送消息
     *
     * @param webSocketDto 消息内容
     * @return 消息发送结果
     * @throws Exception
     */
    @ApiOperation("直接发送消息")
    @PostMapping("/send")
    public JsonResultVo<String> send(@RequestBody WebSocketDto webSocketDto) throws Exception {
        webSocketService.sendMessage(webSocketDto);
        JsonResultVo<String> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage(SYSRES_COMMON_MSGSENDSUCCESS);
        result.setMsg(message);
        return result;
    }

    /**
     * 清空消息相关缓存
     *
     * @return 消息发送结果
     * @throws Exception
     */
    @ApiOperation("清空消息相关缓存")
    @GetMapping("/clearWS")
    public JsonResultVo<String> clearWS() throws Exception {
        webSocketService.clearWS();
        JsonResultVo<String> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage(SYSRES_COMMON_MSGSENDSUCCESS);
        result.setMsg(message);
        return result;
    }

    /**
     * MQ发送消息
     *
     * @param webSocketDto 消息内容
     * @return 消息发送结果
     */
    @ApiOperation("通过MQ发送消息")
    @PostMapping("/sendMessage")
    public JsonResultVo<String> sendMessage(@RequestBody WebSocketDto webSocketDto) {
        webSocketService.sendMessageByMq(webSocketDto);
        JsonResultVo<String> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage(SYSRES_COMMON_MSGSENDSUCCESS);
        result.setMsg(message);
        return result;
    }

    /**
     * 获取在线人数
     *
     * @return
     */
    @ApiOperation("获取在线人数")
    @GetMapping("getThisOnlineNumber")
    public JsonResultVo<Long> getThisOnlineNumber() {
        JsonResultVo<Long> result = new JsonResultVo<>();
        result.setData(webSocketService.getOnlineNumber());
        return result;
    }

    /**
     * 发送即时消息
     *
     * @param sINSMsgDTO 消息内容
     * @return 消息发送结果
     * @throws Exception
     */
    @ApiOperation("发送即时消息")
    @PostMapping("/sendInstanceMsg")
    public JsonResultVo sendInstanceMsg(@RequestBody SendInstantMessageDTO sINSMsgDTO) throws Exception {
        JsonResultVo result = new JsonResultVo<>();
        WebSocketDto webSocketDto = new WebSocketDto();
        webSocketDto.setSendType(sINSMsgDTO.getVSendType());
        if (!BootAppUtil.isNullOrEmpty(sINSMsgDTO.getVToActorId())) {
            webSocketDto.setUserIDs(Arrays.asList(sINSMsgDTO.getVToActorId().split(",")));
            Map<String, Object> messageInfo = new HashMap<>();
            messageInfo.put("msgType", sINSMsgDTO.getVMsgType());
            messageInfo.put("title", sINSMsgDTO.getVTitle());
            messageInfo.put("content", sINSMsgDTO.getVContent());
            messageInfo.put("attchInfo", sINSMsgDTO.getVAttachInfo());
            messageInfo.put("id", sINSMsgDTO.getVID());
            messageInfo.put("count", sINSMsgDTO.getVCount());
            webSocketDto.setMessage(messageInfo);
            List<String> offUsers = webSocketService.sendMessage(webSocketDto);
            result.setData(offUsers);
        } else {
            String message = i18nUtil.getMessage("MSG.sysres.websocket.setSender");
            result.setMsg(message);
        }
        return result;
    }
}
