package com.qm.ep.logs.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class LogInputDTO extends JsonParamDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 单据id
     */
    private String billId;

    /**
     * 访问时间 开始时间
     */
    private String startTime;

    /**
     * 访问时间 结束时间
     */
    private String endTime;

    /**
     * 命中数
     */
    private Integer limit;
}
