package com.qm.ep.sysres.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "SendInstantMessageDTO", description = "即时消息")
public class SendInstantMessageDTO implements Serializable {
    private static final long serialVersionUID = -2272522018802528378L;

    @ApiModelProperty(value = "actorId,多个用逗号分割")
    private String vToActorId;

    @ApiModelProperty("发送类型，1 按照用户发送 2 全部发送 3角色")
    private Integer vSendType;

    @ApiModelProperty(value = "消息类型")
    private String vMsgType;

    @ApiModelProperty(value = "消息主题")
    private String vTitle;

    @ApiModelProperty(value = "消息内容")
    private String vContent;

    @ApiModelProperty(value = "消息附加信息")
    private List<Map<String, String>> vAttachInfo;

    @ApiModelProperty(value = "消息ID")
    private String vID;

    @ApiModelProperty(value = "消息数")
    private String vCount;
}