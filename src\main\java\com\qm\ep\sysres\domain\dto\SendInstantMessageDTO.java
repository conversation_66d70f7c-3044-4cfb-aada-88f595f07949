package com.qm.ep.sysres.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Schema(title = "即时消息", description = "即时消息")
public class SendInstantMessageDTO implements Serializable {
    private static final long serialVersionUID = -2272522018802528378L;

    @Schema(title = "actorId,多个用逗号分割", description = "actorId,多个用逗号分割")
    private String vToActorId;

    @Schema(title = "发送类型，1 按照用户发送 2 全部发送 3角色", description = "发送类型，1 按照用户发送 2 全部发送 3角色")
    private Integer vSendType;

    @Schema(title = "消息类型", description = "消息类型")
    private String vMsgType;

    @Schema(title = "消息主题", description = "消息主题")
    private String vTitle;

    @Schema(title = "消息内容", description = "消息内容")
    private String vContent;

    @Schema(title = "消息附加信息", description = "消息附加信息")
    private List<Map<String, String>> vAttachInfo;

    @Schema(title = "消息ID", description = "消息ID")
    private String vID;

    @Schema(title = "消息数", description = "消息数")
    private String vCount;
}