package com.qm.ep.sysres.service.impl;

import com.obs.services.ObsClient;
import com.obs.services.model.ObsObject;
import com.qcloud.cos.utils.IOUtils;
import com.qm.ep.sysres.service.LocalFileOperator;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.ImageUtil;
import com.qm.tds.base.file.AbstractFileOperator;
import com.qm.tds.base.file.ObsOperator;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

@Component
@Lazy
public class LocalObsOperator extends AbstractFileOperator implements LocalFileOperator {
    private static final Logger log = LoggerFactory.getLogger(ObsOperator.class);
    @Autowired
    private ImageUtil imageUtil;
    @Value("${qm.base.upload.cos-secretId:null}")
    private String AK;
    @Value("${qm.base.upload.cos-secretKey:null}")
    private String SK;
    @Value("${qm.base.upload.cos-regionName:null}")
    private String ENDPOINT;
    @Value("${qm.base.upload.cos-bucket:null}")
    private String BUCKET_NAME;
    @Value("${qm.base.upload.bucket-path:null}")
    private String BUCKET_PATH;
    private static ObsClient obsClient;


    public ObsClient getObsClient() {
        if (null == obsClient) {
            obsClient = new ObsClient(this.AK, this.SK, this.ENDPOINT);
        }

        return obsClient;
    }

    public boolean downloadFile(HttpServletResponse response, String vaddr) {
        boolean result = false;

        try {
            ObsObject obsObject = this.getObsClient().getObject(this.BUCKET_NAME, this.BUCKET_PATH + "/" + vaddr);
            if (obsObject != null) {
                InputStream input = obsObject.getObjectContent();
                if (input != null) {
                    byte[] b = IOUtils.toByteArray(input);
                    response.setContentLengthLong((long)b.length);
                    response.addHeader("Content-Range", "bytes 0-" + b.length + "/" + b.length);
                    StreamUtils.copy(b, response.getOutputStream());
                    result = true;
                } else {
                    log.info("---error--下载文件不在OBS服务器上[" + vaddr + "]");
                }
            }
        } catch (Exception var7) {
            Exception e = var7;
            log.info("---error--downloadFile-异常ObsException:" + e.getMessage());
        }

        return result;
    }

    public boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int widthInt, int heighInt, UploadFileVO uploadFileVO) {
        Boolean result = false;

        try {
            String fullPathNameAndSuffix = uploadFileVO.getVaddr();
            if (fullPathNameAndSuffix.indexOf(46) > -1) {
                switch (thumbnailFlag) {
                    case "small":
                        String fullPath = fullPathNameAndSuffix.substring(0, fullPathNameAndSuffix.lastIndexOf("/"));
                        String nameAndSuffix = fullPathNameAndSuffix.substring(fullPathNameAndSuffix.lastIndexOf("/") + 1);
                        String fileName = nameAndSuffix.substring(0, nameAndSuffix.lastIndexOf("."));
                        String suffix = nameAndSuffix.substring(nameAndSuffix.lastIndexOf("."));
                        if (!StringUtils.isEmpty(uploadFileVO.getVcontenttype()) && uploadFileVO.getVcontenttype().contains("video") && "small".equals(thumbnailFlag)) {
                            this.cutPhotoFromVedio(response, fullPathNameAndSuffix, widthInt, heighInt);
                        } else {
                            String smallImageName = fileName + "_" + widthInt + "_" + heighInt + suffix;
                            String smallImagePathName = fullPath + "/" + smallImageName;
                            result = this.obsDownload(response, smallImagePathName, fullPathNameAndSuffix, widthInt, heighInt, uploadFileVO);
                        }
                        break;
                    case "normal":
                        result = this.downloadFile(response, fullPathNameAndSuffix);
                }
            } else {
                result = this.downloadFile(response, fullPathNameAndSuffix);
            }
        } catch (QmException var16) {
            QmException e = var16;
            throw e;
        } catch (Exception var17) {
            Exception e = var17;
            log.info("---error--下载出错", e);
            throw new QmException("文件下载操作错误，请查看日志;", e);
        }

        return result;
    }

    private Boolean obsDownload(HttpServletResponse response, String smallImagePathName, String fullPathNameAndSuffix, int width, int heigh, UploadFileVO uploadFilevO) throws IOException {
        String fileName = smallImagePathName.substring(smallImagePathName.lastIndexOf("/") + 1);
        boolean result = false;

        try {
            if (uploadFilevO != null && !BootAppUtil.isNullOrEmpty(uploadFilevO.getVfiletype()) && "PIC".equalsIgnoreCase(uploadFilevO.getVfiletype())) {
                String objectKey = this.BUCKET_PATH + "/" + smallImagePathName;
                log.info("文件id:" + uploadFilevO.getId()+",---桶名:" + this.BUCKET_NAME + ",---objectKey:" + objectKey);
                boolean flag = this.getObsClient().doesObjectExist(this.BUCKET_NAME, objectKey);
                if (!flag) {
                    log.info("---error--下载文件不在OBS服务器上[" + smallImagePathName + "]");
                    this.getthumbanailImageInputStream(smallImagePathName, fullPathNameAndSuffix, ImageUtil.getSuffix(fileName), width, heigh);
                }


                ObsObject obsObject = this.getObsClient().getObject(this.BUCKET_NAME, objectKey);

                InputStream input = obsObject.getObjectContent();
                if (input != null) {
                    byte[] b = IOUtils.toByteArray(input);
                    if (ArrayUtils.isEmpty(b)) {
                        throw new QmException("下载文件在OBS上不存在");
                    }

                    response.setContentLengthLong((long)b.length);
                    response.addHeader("Content-Range", "bytes 0-" + b.length + "/" + b.length);
                    StreamUtils.copy(b, response.getOutputStream());
                    result = true;
                } else {
                    log.info("---error--下载文件不在OBS服务器上[" + smallImagePathName + "]");
                }
            } else {
                log.info("---error--uploadFileVO为空或者uploadFileVO.vfiletype为空");
            }
        } catch (Exception var13) {
            Exception e = var13;
            log.info("---error--obsDownload下载文件异常Exception:" + e.getMessage());
        }

        return result;
    }

    private Boolean getthumbanailImageInputStream(String allfileName, String allfileNormalName, String fix, int width, int height) throws IOException {
        boolean result = false;

        try {
            ObsObject obsObject = this.getObsClient().getObject(this.BUCKET_NAME, this.BUCKET_PATH + "/" + allfileNormalName);
            InputStream input = obsObject.getObjectContent();
            if (input != null) {
                byte[] isb = IOUtils.toByteArray(input);
                if (ArrayUtils.isNotEmpty(isb)) {
                    InputStream isNormal = new ByteArrayInputStream(isb);
                    InputStream is = this.imageUtil.thumbanailImage(isNormal, fix, width, height);
                    this.getObsClient().putObject(this.BUCKET_NAME, this.BUCKET_PATH + "/" + allfileName, is);
                    result = true;
                } else {
                    log.info("---error--下载文件不在OBS服务器上[" + allfileNormalName + "]");
                }
            } else {
                log.info("---error--下载文件不在OBS服务器上[" + allfileNormalName + "]");
            }
        } catch (Exception var12) {
            Exception e = var12;
            log.info("---error--getthumbanailImageInputStream下载文件异常Exception:" + e.getMessage());
        }

        return result;
    }

    public String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile) {
        try {
            InputStream inputStream = multipartFile.getInputStream();
            this.getObsClient().putObject(this.BUCKET_NAME, this.BUCKET_PATH + "/" + basePath + "/" + fileSaveName, inputStream);
        } catch (Exception var5) {
            Exception e = var5;
            log.info("---error--OBS上传文件报错Exception:" + e.getMessage());
        }

        return basePath;
    }

    public byte[] getFileByte(String filePath) {
        try {
            ObsObject obsObject = this.getObsClient().getObject(this.BUCKET_NAME, this.BUCKET_PATH + "/" + filePath);
            InputStream input = obsObject.getObjectContent();
            return IOUtils.toByteArray(input);
        } catch (IOException var4) {
            IOException e = var4;
            log.info("---error--" + e.getMessage(), e);
            return new byte[0];
        }
    }
}
