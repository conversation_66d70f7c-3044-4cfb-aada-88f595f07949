#这里填写俄文翻译
MSG.sysres.common.uploadSuccess=Загрузка удалась!
ERR.sysres.common.uploadFail=Загрузка не удалась!
MSG.sysres.common.copySuccess=Copy удалось
MSG.sysres.common.msgSendSuccess=Отправка SMS-сообщений удалась
MSG.sysres.websocket.setSender=Указать отправителя!
MSG.sysres.pdfFileImpl.localTitle=Источник статистических данных: Пример «Система EP - единый журнал – анализ производительности интерфейса»
ERR.sysres.tableService.getPageFail=Не удалось разбиение страницы doExport для получения данных：
MSG.sysres.tableService.serviceNotFound=Служба %s не найдена.
MSG.sysres.tableService.enterDownload=Нажать %s, чтобы загрузить файл.
MSG.sysres.tableService.exportFinish=Вывод завершен
##################################=
ERR.sysres.WebSocketDto.msgNull=Отправляемое сообщение не должно быть пустым
ERR.sysres.AttachCopyBillVO.originTicketIdNull=Id исходного документа не должен быть пустым
ERR.sysres.AttachCopyBillVO.newTicketIdNull=Id нового документа не должен быть пустым
MSG.sysres.WebSocketCServer.mqSendFail=Распространение сообщения MQ не удалось.
MSG.sysres.WebSocketCServer.heartbeatResponse=Реакция на команду Ping
MSG.sysres.PdfFileImpl.commonLogCount=Источник статистических данных: Пример «Система EP - единый журнал – анализ производительности интерфейса»
MSG.sysres.PdfFileImpl.createBy=Создано %s
MSG.sysres.PdfFileImpl.checker=Лицо, отвечающее за инспекционный контроль
MSG.sysres.PdfFileImpl.checkTime=Время инспекционного контроля
MSG.sysres.PdfFileImpl.countPeriod=Периодичность статистики
MSG.sysres.PdfFileImpl.systemEnv=Системная среда
MSG.sysres.PdfFileImpl.performanceCheckList=1. Список инспекционного контроля производительности интерфейса
MSG.sysres.PdfFileImpl.averageCostTimeTop=Среднее затраченное время Топ10
MSG.sysres.PdfFileImpl.serverName=Имя службы
MSG.sysres.PdfFileImpl.interfaceUri=URI Интерфейса
MSG.sysres.PdfFileImpl.successRate=Коэффициент успеха
MSG.sysres.PdfFileImpl.averageCostTime=Среднее затраченное время (мс)
MSG.sysres.PdfFileImpl.runTimes=Объем выполнения (раз)
MSG.sysres.PdfFileImpl.handler=Обработчик
MSG.sysres.PdfFileImpl.handleTime=Время обработки
MSG.sysres.PdfFileImpl.remarks=Примечание
MSG.sysres.PdfFileImpl.costTimeTop=Затраченное время Топ10
MSG.sysres.PdfFileImpl.costTime=Время выполнения
MSG.sysres.PdfFileImpl.costTimems=Время, затраченное на выполнение (мс)
MSG.sysres.PdfFileImpl.exceptionTop=1.3. Исключение услуг Топ10
MSG.sysres.PdfFileImpl.exceptionInfo=Информация об исключениях
MSG.sysres.PdfFileImpl.exceptionAccount=Количество исключений
MSG.sysres.PdfFileImpl.searchHelpPerformanceList=2. Список инспекционного контроля производительности средства поиска
MSG.sysres.PdfFileImpl.searchHelpName=Имя средства поиска
MSG.sysres.PdfFileImpl.databasePerformanceList=3. Список инспекционного контроля производительности базы данных
MSG.sysres.PdfFileImpl.showSqlTop=3.1 Медленный SqlTop10
MSG.sysres.PdfFileImpl.showSqlCount=Источник статистических данных: журнал медленного SQL базы данных, пример «Система EP - единый журнал – журнал медленного SQL»
MSG.sysres.PdfFileImpl.clientIp=IP-адрес клиента
MSG.sysres.PdfFileImpl.databaseAddress=Адрес базы данных
MSG.sysres.PdfFileImpl.databaseInstance=Пример базы данных
MSG.sysres.PdfFileImpl.returnRecordCount=Количество возвращенных записей
MSG.sysres.PdfFileImpl.quartzTask=4. Таймированная задача
MSG.sysres.PdfFileImpl.exceptionCount=4.1. Статистика исключений Топ-10
MSG.sysres.PdfFileImpl.quartzLogCount=Источник статистических данных: «Система EP - единый журнал – журнал таймированной задачи»
MSG.sysres.PdfFileImpl.name=Название
MSG.sysres.PdfFileImpl.failTimes=Количество раз неуспеха
MSG.sysres.PdfFileImpl.execCountTop=4.2. Статистика выполнений Топ10
MSG.sysres.PdfFileImpl.execTimes=Количество раз выполнения
MSG.sysres.MyHeaderFooter.pageStartEnd=Я - верхний / нижний колонтитул
MSG.sysres.MyHeaderFooter.currentPage=Страница № %s/
MSG.sysres.MyHeaderFooter.totalPage=Общая страница %s
#############################=
ERR.message.MessageModuleEnum.mobile=Номер мобильного телефона
ERR.message.MessageModuleEnum.emailAddress=Адрес электронной почты
ERR.message.MessageModuleEnum.wxAccount=Аккаунт в WeChat
ERR.message.MessageModuleEnum.dingAccount=Номер DingTalk
ERR.message.DingTemplateDTO.messageTitleNull=Заголовок сообщения не должен быть пустым!
ERR.message.DingTemplateDTO.messageContentNull=Контент сообщения не должен быть пустым!
ERR.message.DingTemplateDTO.redirectUrlNull=URL-адрес для перехода не должен быть пустым!
ERR.message.DingTemplateDTO.methodContent202=Контент метода 202!
ERR.message.MessageTemplateSendDTO.publishDateNull=Дата выпуска не должна быть пустой.
ERR.message.MessageTemplateSendDTO.objectDetailNull=Ведомость об объекте не должна быть пустой.
ERR.message.MessageTemplateSendUserDTO.busiSysCodeNull=Номер операционной системы не должен быть пустым.
ERR.message.SendDingDTO.dingIdNull=Id пользователя DingTalk не должен быть пустым!
ERR.message.SendDingEPDTO.epsysIdNull=Id пользователя системы EP не должен быть пустым!
ERR.message.SendDingEPDTO.epsysCompanyIdNull=ID компании, к которой относится id пользователя системы EP!
ERR.message.SendMailDTO.emailReceiverNull=Адрес получателя электронной почты не должен быть пустым!
ERR.message.SendSmsDTO.smsReceiverNull=Адрес получателя SMS-сообщений не должен быть пустым!
ERR.message.SendSmsDTO.templateCodeNull=Номер шаблона не должен быть пустым!
ERR.message.SendWxTemplateDTO.wxReceiverAddressNull=Адрес получателя WeChat не должен быть пустым!
ERR.message.SendWxTemplateDTO.templateContentNull=Контент шаблона не должен быть пустым!
ERR.message.VerificationDTO.mobileNull=Номер мобильного телефона не должен быть пустым!
ERR.message.MessageFactory.messageFactoryNoTemplate=Фабрика SMS-сообщений не определила шаблона：
