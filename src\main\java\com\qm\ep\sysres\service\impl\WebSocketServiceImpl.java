package com.qm.ep.sysres.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qm.ep.sysres.constant.SocketConstant;
import com.qm.ep.sysres.domain.dto.WebSocketDto;
import com.qm.ep.sysres.server.WebSocketCServer;
import com.qm.ep.sysres.server.WebSocketServer;
import com.qm.ep.sysres.service.WebSocketService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.mq.builder.DefaultDestination;
import com.qm.tds.mq.builder.DefaultTxMessage;
import com.qm.tds.mq.builder.MessageStruct;
import com.qm.tds.mq.constant.ExchangeType;
import com.qm.tds.mq.message.MessageSendStruct;
import com.qm.tds.mq.remote.MqFeignRemote;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;


@Service
@Slf4j
public class WebSocketServiceImpl implements WebSocketService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private WebSocketCServer webSocketServer;

    @SuppressWarnings("squid:S3776")
    @Override
    public List<String> sendMessage(WebSocketDto webSocketDto) throws IOException {
        // 按照用户发送
        List<String> offLineUsers = new ArrayList<>();
        if (webSocketDto.getSendType() == 1 && !webSocketDto.getUserIDs().isEmpty()) {
            for (String userID : webSocketDto.getUserIDs()) {
                // 根据userId模糊查询redis内所有在线的用户
                String redisKey = redisUtils.keyBuilder(SocketConstant.REDIS_KEY_PRE, SocketConstant.SOCKET_USER, userID + "*");
                // 获取用户所有登陆的key
                Set<String> users = redisUtils.getKeys(redisKey);
                if (users.isEmpty()) {
                    offLineUsers.add(userID);
                    continue;
                }
                log.debug("根据userid：" + userID + ",查找到如下用户会话：" + String.join(",", users));
                for (String userKey : users) {
                    Object user = redisUtils.get(userKey);
                    if (!BootAppUtil.isNullOrEmpty(userKey)) {
                        webSocketDto.setUserStamp(user.toString());
                        pushByOne((webSocketDto), user.toString());
                    } else {
                        offLineUsers.add(userID);
                    }
                }
            }
        }
        // 发送给全部用户
        if (webSocketDto.getSendType() == 2) {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(webSocketDto));
            pushByAll(jsonObject);
        }
        return offLineUsers;
    }

    /**
     * 获取在线人数
     *
     * @return
     */
    @Override
    public Long getOnlineNumber() {
        String redisKey = redisUtils.keyBuilder(SocketConstant.REDIS_KEY_PRE, SocketConstant.SOCKET_USER, "*");
        Set<String> keys = redisUtils.getKeys(redisKey);
        log.info("在线key:{}", keys);
        int number = keys.size();
        return (long) number;
    }

    @Override
    public void updateThisOnlineNumber() {
        // Do nothing because of X and Y.
    }

    @Override
    public void deleteCloseLink() {
        webSocketServer.deleteCloseLink();
    }

    @Override
    public void clearWS() {
        webSocketServer.clearWS();
    }

    @Override
    public void sendMessageByMq(WebSocketDto webSocketDto) {
        webSocketServer.sendMessageByMq(webSocketDto);
    }


    /**
     * 系统（所有在线的）
     *
     * @param jsonObject
     */
    void pushByAll(JSONObject jsonObject) throws IOException {
        webSocketServer.pushByAll(jsonObject);
    }

    /**
     * 单发
     *
     * @param webSocketObject
     * @param userID
     */
    void pushByOne(WebSocketDto webSocketObject, String userID) throws IOException {
        webSocketServer.pushByOne(webSocketObject, userID);
    }
}
