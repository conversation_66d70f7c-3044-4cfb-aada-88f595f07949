package com.qm.ep.sysres.remote;

import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.api.mp.pagination.QmPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 执行统计
 *
 * <AUTHOR>
 * @date 20210831
 */
@Component
@Slf4j
public class CommonServiceQuartzRemoteHystrix extends QmRemoteHystrix<CommonServiceQuartzRemote> implements CommonServiceQuartzRemote {

    @Override
    public JsonResultVo executionStatistics(@RequestBody QuartzDTO dto) {
        return getResult();
    }

    @Override
    public JsonResultVo<QmPage<QuartzStatisticsVO>> executionStatisticsQuery(@RequestBody QuartzDTO dto) {
        return getResult();
    }
}
