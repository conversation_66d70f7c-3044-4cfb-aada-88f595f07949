package com.qm.ep.sysres.remote;

import com.qm.ep.sysres.domain.dto.MessageSendNoticeDTO;
import com.qm.ep.sysres.domain.po.MessageNoticePO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;

@Repository
@FeignClient(name = "common-service-message-temple", fallbackFactory = NoticeFactory.class)
public interface NoticeRemote {

    @PostMapping(value = "/notice/getIdWorkerSTR")
    JsonResultVo<String> getIdWorkerSTR(@RequestBody @Valid String vModelName);

    @PostMapping(value = "/notice/saveNoticeByOtherUse")
    JsonResultVo<MessageNoticePO> saveNoticeByOtherUse(@RequestBody MessageSendNoticeDTO messageSendNoticeDTO);
}
