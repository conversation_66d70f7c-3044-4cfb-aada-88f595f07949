package com.qm.ep.sysres.service.impl;

import com.itextpdf.io.source.ByteArrayOutputStream;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.GrayColor;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.qm.ep.logs.domain.dto.*;
import com.qm.ep.logs.domain.es.GateWayLog;
import com.qm.ep.logs.domain.es.SlowSqlLog;
import com.qm.ep.logs.domain.vo.SearchVO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.ep.sys.domain.dto.AttachImputItemDTO;
import com.qm.ep.sysres.config.LogPdfConfig;
import com.qm.ep.sysres.domain.bean.AttachFileDO;
import com.qm.ep.sysres.domain.vo.AttachCenterFileVO;
import com.qm.ep.sysres.domain.vo.AttachFileCenterVO;
import com.qm.ep.sysres.remote.EpSysRemote;
import com.qm.ep.sysres.service.AttachFileService;
import com.qm.ep.sysres.service.PdfFileService;
import com.qm.ep.sysres.utils.MyHeaderFooter;
import com.qm.ep.sysres.utils.PdfUtil;
import com.qm.ep.sysres.utils.Watermark;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.domain.MultipartFileDecorator;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.qm.tds.util.DateUtils.DATE_TIME_PATTERN;

@Service
@Slf4j
public class PdfFileImpl implements PdfFileService {
    @Autowired
    private EpSysRemote epSysRemote;
    @Autowired
    private LogPdfConfig logPdfConfig;
    @Autowired
    private AttachFileService attachFileService;
    @Autowired
    private I18nUtil i18nUtil;
    // 定义全局的字体静态变量
    private static com.itextpdf.text.Font titlefont;
    private static com.itextpdf.text.Font headfont;
    private static com.itextpdf.text.Font keyfont;
    private static com.itextpdf.text.Font textfont;
    private static com.itextpdf.text.Font titlefontGay;
    private static com.itextpdf.text.Font headfontGay;
    private static com.itextpdf.text.Font keyfontGay;
    private static com.itextpdf.text.Font textfontGay;
    // 最大宽度
    private static int maxWidth = 520;
    //pdf操作类
    private PdfUtil pdfUtil = new PdfUtil();

    private String localStartTime = "startTime";
    private String localEndTime = "endTime";
    private String localLog = "LOGEXPORT";
    private String localCount = "count";

    // 静态代码块
    static {
        try {
            // 不同字体（这里定义为同一种字体：包含不同字号、不同style）
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            titlefont = new com.itextpdf.text.Font(bfChinese, 18, com.itextpdf.text.Font.BOLD);
            headfont = new com.itextpdf.text.Font(bfChinese, 15, com.itextpdf.text.Font.BOLD);
            keyfont = new com.itextpdf.text.Font(bfChinese, 12, com.itextpdf.text.Font.BOLD);
            textfont = new com.itextpdf.text.Font(bfChinese, 6, com.itextpdf.text.Font.NORMAL);
            titlefontGay = new com.itextpdf.text.Font(bfChinese, 18, com.itextpdf.text.Font.BOLD, new GrayColor(0.50f));
            headfontGay = new com.itextpdf.text.Font(bfChinese, 15, com.itextpdf.text.Font.BOLD, new GrayColor(0.50f));
            keyfontGay = new com.itextpdf.text.Font(bfChinese, 12, com.itextpdf.text.Font.BOLD, new GrayColor(0.50f));
            textfontGay = new com.itextpdf.text.Font(bfChinese, 6, com.itextpdf.text.Font.NORMAL, new GrayColor(0.50f));
        } catch (Exception ex) {
            log.info("---error--"+"导出统一日志导出PDF文件-字体设置，" + ex.getMessage(), ex);
        }
    }


    /**
     * 下载附件
     *
     * @param request
     * @param response
     * @return
     */
    @Override
    public Integer upDownExportPDF(HttpServletRequest request, HttpServletResponse response) {
        Integer resultVo = 0;
        Boolean flage = false;
        try {
            if (!BootAppUtil.isNullOrEmpty(request.getParameter("Nbillid")) && !BootAppUtil.isNullOrEmpty(request.getParameter(localStartTime)) && !BootAppUtil.isNullOrEmpty(request.getParameter(localEndTime))) {

                String nbillId = request.getParameter("Nbillid");
                AttachImputItemDTO attachImputItemDTO = new AttachImputItemDTO();
                attachImputItemDTO.setNbillid(nbillId);
                attachImputItemDTO.setUsemode("1");
                attachImputItemDTO.setVbustype(localLog);
                attachImputItemDTO.setVmntnmode("file");
                JsonResultVo<AttachFileCenterVO> attachFileCenterVOJsonResultVo = epSysRemote.getAttachByBustype(BootAppUtil.getLoginKey().getTenantId(), attachImputItemDTO);
                AttachFileCenterVO attachFileCenterVO = attachFileCenterVOJsonResultVo.getData();
                List<AttachCenterFileVO> attachCenterFileVOList = attachFileCenterVO.getAttachCenterFileVOList();
                if (attachCenterFileVOList.isEmpty()) {
                    AnalyseDto analyseDto = new AnalyseDto();
                    GateWayLogDTO gateWayLogDTO01 = new GateWayLogDTO();
                    SysErrorDTO sysErrorDTO = new SysErrorDTO();
                    SearchHelpAnalyseDto searchHelpAnalyseDto = new SearchHelpAnalyseDto();
                    GateWayLogDTO gateWayLogDTO02 = new GateWayLogDTO();
                    SlowSqlLogDTO slowSqlLogDTO = new SlowSqlLogDTO();
                    QuartzDTO dto01 = new QuartzDTO();
                    PdfInputDTO pdfInputDTO = new PdfInputDTO();

                    pdfInputDTO.setStartTime(request.getParameter(localStartTime));
                    pdfInputDTO.setEndTime(request.getParameter(localEndTime));
//                    1
                    analyseDto.setStartTime(request.getParameter(localStartTime));
                    analyseDto.setEndTime(request.getParameter(localEndTime));
                    analyseDto.setLimit(logPdfConfig.getLimit());
                    analyseDto.setLogServerName(logPdfConfig.getLogServerName());
                    JsonResultVo<List<Map<String, String>>> listJsonResultVo = attachFileService.timeConsumingAnalysis(analyseDto);
                    List<Map<String, String>> mapListgatewayTimeConsumingAnalysis = listJsonResultVo.getData();
                    pdfInputDTO.setMapListgatewayTimeConsumingAnalysis(mapListgatewayTimeConsumingAnalysis);
//                    2
                    gateWayLogDTO01.setCurrentPage(logPdfConfig.getCurrentPage());
                    gateWayLogDTO01.setPageSize(logPdfConfig.getPageSize());
                    gateWayLogDTO01.setStartTime(request.getParameter(localStartTime));
                    gateWayLogDTO01.setEndTime(request.getParameter(localEndTime));
                    gateWayLogDTO01.setTsortby(logPdfConfig.getTsortby());
                    JsonResultVo<QmPage<GateWayLog>> qmPageJsonResultVo = attachFileService.gatewayLogsQuery(gateWayLogDTO01);
                    QmPage<GateWayLog> gateWayLogQmPage = qmPageJsonResultVo.getData();
                    List<GateWayLog> gateWayLogListgatewayLogsQuery = gateWayLogQmPage.getItems();
                    pdfInputDTO.setGateWayLogListgatewayLogsQuery(gateWayLogListgatewayLogsQuery);
//                    3
                    sysErrorDTO.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    sysErrorDTO.setPageSize((int) logPdfConfig.getPageSize());
                    sysErrorDTO.setStartDateTime(request.getParameter(localStartTime));
                    sysErrorDTO.setEndDateTime(request.getParameter(localEndTime));
                    sysErrorDTO.setLimit(logPdfConfig.getLimit());
                    sysErrorDTO.setLogMsgSub(logPdfConfig.getLogMsgSub().toArray(new String[logPdfConfig.getLogMsgSub().size()]));
                    JsonResultVo<List<Map<String, String>>> listJsonResultVo1 = attachFileService.sysErrorTimeConsumingAnalysis(sysErrorDTO);
                    List<Map<String, String>> mapListsysErrorTimeConsumingAnalysis = listJsonResultVo1.getData();
                    pdfInputDTO.setMapListsysErrorTimeConsumingAnalysis(mapListsysErrorTimeConsumingAnalysis);
//                    4
                    searchHelpAnalyseDto.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    searchHelpAnalyseDto.setPageSize((int) logPdfConfig.getPageSize());
                    searchHelpAnalyseDto.setStartTime(request.getParameter(localStartTime));
                    searchHelpAnalyseDto.setEndTime(request.getParameter(localEndTime));
                    searchHelpAnalyseDto.setLimit(logPdfConfig.getLimit());
                    JsonResultVo<List<Map<String, String>>> listJsonResultVo2 = attachFileService.searchHelpAnalysis(searchHelpAnalyseDto);
                    List<Map<String, String>> mapListsearchHelpAnalysis = listJsonResultVo2.getData();
                    pdfInputDTO.setMapListsearchHelpAnalysis(mapListsearchHelpAnalysis);
//                    5
                    gateWayLogDTO02.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    gateWayLogDTO02.setPageSize((int) logPdfConfig.getPageSize());
                    gateWayLogDTO02.setStartTime(request.getParameter(localStartTime));
                    gateWayLogDTO02.setEndTime(request.getParameter(localEndTime));
                    JsonResultVo<QmPage<SearchVO>> qmPageJsonResultVo1 = attachFileService.searchQueryTime(gateWayLogDTO02);
                    QmPage<SearchVO> searchVOQmPage = qmPageJsonResultVo1.getData();
                    List<SearchVO> searchVOListsearchQueryTime = searchVOQmPage.getItems();
                    pdfInputDTO.setSearchVOListsearchQueryTime(searchVOListsearchQueryTime);
//                    6
                    slowSqlLogDTO.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    slowSqlLogDTO.setPageSize((int) logPdfConfig.getPageSize());
                    slowSqlLogDTO.setStartDateTime(request.getParameter(localStartTime));
                    slowSqlLogDTO.setEndtDateTime(request.getParameter(localEndTime));
                    slowSqlLogDTO.setTsortby(logPdfConfig.getTsortbyslow());
                    JsonResultVo<QmPage<SlowSqlLog>> qmPageJsonResultVo2 = attachFileService.slowSqlLogQuery(slowSqlLogDTO);
                    QmPage<SlowSqlLog> slowSqlLogQmPage = qmPageJsonResultVo2.getData();
                    List<SlowSqlLog> slowSqlLogList = slowSqlLogQmPage.getItems();
                    pdfInputDTO.setSlowSqlLogList(slowSqlLogList);
//                    7
                    dto01.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    dto01.setPageSize((int) logPdfConfig.getPageSize());
                    dto01.setStartTime(request.getParameter(localStartTime));
                    dto01.setEndTime(request.getParameter(localEndTime));
                    dto01.setStatusList(logPdfConfig.getStatusList());
                    JsonResultVo<QmPage<QuartzStatisticsVO>> qmPageJsonResultVo3 = attachFileService.executionStatisticsQuery(dto01);
                    QmPage<QuartzStatisticsVO> quartzStatisticsVOQmPage = qmPageJsonResultVo3.getData();
                    List<QuartzStatisticsVO> quartzStatisticsVOStatusList01 = quartzStatisticsVOQmPage.getItems();
                    pdfInputDTO.setQuartzStatisticsVOStatusList01(quartzStatisticsVOStatusList01);
//                    8
                    List<String> stringList = new ArrayList<>();
                    dto01.setStatusList(stringList);
                    JsonResultVo<QmPage<QuartzStatisticsVO>> qmPageJsonResultVo4 = attachFileService.executionStatisticsQuery(dto01);
                    QmPage<QuartzStatisticsVO> quartzStatisticsVOQmPage01 = qmPageJsonResultVo4.getData();
                    List<QuartzStatisticsVO> quartzStatisticsVOStatusList02 = quartzStatisticsVOQmPage01.getItems();
                    pdfInputDTO.setQuartzStatisticsVOStatusList02(quartzStatisticsVOStatusList02);

                    ByteArrayOutputStream byteArrayOutputStream = getOutputStreamUsePDF(pdfInputDTO);
                    String pdfFileName = nbillId + "_" + logPdfConfig.getPdfname() + "_" + logPdfConfig.getPdfev() + ".pdf";
                    /**pdfDocumentCreateNew(byteArrayOutputStream, pdfFileName);
                     *resultVo = 1;
                     */
                    MultipartFileDecorator mfd = new MultipartFileDecorator(byteArrayOutputStream.toByteArray(), pdfFileName, pdfFileName);
                    AttachFileDO fileDO = attachFileService.upload(mfd, nbillId, localLog, "0");
                    if (!BootAppUtil.isNullOrEmpty(fileDO)) {
                        resultVo = 1;
                    }
                }
                flage = attachFileService.download(request, response);
                if (Boolean.TRUE.equals(flage)) {
                    resultVo = 1;
                }
            }
        } catch (Exception ex) {
            log.info("---error--"+"导出统一日志导出PDF文件主流程异常，" + ex.getMessage(), ex);
        }
        return resultVo;
    }


    public ByteArrayOutputStream getOutputStreamUsePDF(PdfInputDTO pdfInputDTO) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            // 1.新建document对象
            Document document = new Document(PageSize.A4);// 建立一个Document对象

            // 2.建立一个书写器(Writer)与document对象关联
            PdfWriter writer = PdfWriter.getInstance(document, byteArrayOutputStream);
            writer.setPageEvent(new Watermark(logPdfConfig.getWatermark()));// 水印
            writer.setPageEvent(new MyHeaderFooter());// 页眉/页脚

            // 3.打开文档
            document.open();
            document.addTitle(logPdfConfig.getTitle());// 标题
            document.addAuthor(logPdfConfig.getAuthor());// 作者
            document.addSubject(logPdfConfig.getSubject());// 主题
            document.addKeywords(logPdfConfig.getKeywords());// 关键字
            document.addCreator(logPdfConfig.getCreator());// 创建者

            generateEpTestPdf(document, pdfInputDTO);

            document.close();
        } catch (Exception ex) {
            log.info("---error--"+"导出统一日志导出PDF文件子流程异常，" + ex.getMessage(), ex);
        }
        return byteArrayOutputStream;
    }

    @Override
    public AttachFileCenterVO getDownExportPdfId(LogInputDTO logInputDTO) {
        AttachFileCenterVO attachFileCenterVO = new AttachFileCenterVO();
        JsonResultVo<AttachFileCenterVO> attachFileCenterVOJsonResultVo = new JsonResultVo<>();
        Boolean flage = false;
        try {
            if (!BootAppUtil.isNullOrEmpty(logInputDTO.getBillId()) && !BootAppUtil.isNullOrEmpty(logInputDTO.getStartTime()) && !BootAppUtil.isNullOrEmpty(logInputDTO.getEndTime())) {
                /**用截止日期作为id
                 String nbillId = (request.getParameter("endTime").substring(0, 10) + request.getParameter("endTime").substring(0, 10)).replace("-", "");*/
                String nbillId = logInputDTO.getBillId();
                AttachImputItemDTO attachImputItemDTO = new AttachImputItemDTO();
                attachImputItemDTO.setNbillid(nbillId);
                attachImputItemDTO.setUsemode("1");
                attachImputItemDTO.setVbustype(localLog);
                attachImputItemDTO.setVmntnmode("file");
                attachFileCenterVOJsonResultVo = epSysRemote.getAttachByBustype(BootAppUtil.getLoginKey().getTenantId(), attachImputItemDTO);
                attachFileCenterVO = attachFileCenterVOJsonResultVo.getData();
                List<AttachCenterFileVO> attachCenterFileVOList = attachFileCenterVO.getAttachCenterFileVOList();
                if (attachCenterFileVOList.isEmpty()) {
                    AnalyseDto analyseDto = new AnalyseDto();
                    GateWayLogDTO gateWayLogDTO01 = new GateWayLogDTO();
                    SysErrorDTO sysErrorDTO = new SysErrorDTO();
                    SearchHelpAnalyseDto searchHelpAnalyseDto = new SearchHelpAnalyseDto();
                    GateWayLogDTO gateWayLogDTO02 = new GateWayLogDTO();
                    SlowSqlLogDTO slowSqlLogDTO = new SlowSqlLogDTO();
                    QuartzDTO dto01 = new QuartzDTO();
                    PdfInputDTO pdfInputDTO = new PdfInputDTO();

                    pdfInputDTO.setStartTime(logInputDTO.getStartTime());
                    pdfInputDTO.setEndTime(logInputDTO.getEndTime());
//                    1
                    analyseDto.setStartTime(logInputDTO.getStartTime());
                    analyseDto.setEndTime(logInputDTO.getEndTime());
                    analyseDto.setLimit(logPdfConfig.getLimit());
                    analyseDto.setLogServerName(logPdfConfig.getLogServerName());
                    JsonResultVo<List<Map<String, String>>> listJsonResultVo = attachFileService.timeConsumingAnalysis(analyseDto);
                    List<Map<String, String>> mapListgatewayTimeConsumingAnalysis = listJsonResultVo.getData();
                    pdfInputDTO.setMapListgatewayTimeConsumingAnalysis(mapListgatewayTimeConsumingAnalysis);
//                    2
                    gateWayLogDTO01.setCurrentPage(logPdfConfig.getCurrentPage());
                    gateWayLogDTO01.setPageSize(logPdfConfig.getPageSize());
                    gateWayLogDTO01.setStartTime(logInputDTO.getStartTime());
                    gateWayLogDTO01.setEndTime(logInputDTO.getEndTime());
                    gateWayLogDTO01.setTsortby(logPdfConfig.getTsortby());
                    JsonResultVo<QmPage<GateWayLog>> qmPageJsonResultVo = attachFileService.gatewayLogsQuery(gateWayLogDTO01);
                    QmPage<GateWayLog> gateWayLogQmPage = qmPageJsonResultVo.getData();
                    List<GateWayLog> gateWayLogListgatewayLogsQuery = gateWayLogQmPage.getItems();
                    pdfInputDTO.setGateWayLogListgatewayLogsQuery(gateWayLogListgatewayLogsQuery);
//                    3
                    sysErrorDTO.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    sysErrorDTO.setPageSize((int) logPdfConfig.getPageSize());
                    sysErrorDTO.setStartDateTime(logInputDTO.getStartTime());
                    sysErrorDTO.setEndDateTime(logInputDTO.getEndTime());
                    sysErrorDTO.setLimit(logPdfConfig.getLimit());
                    sysErrorDTO.setLogMsgSub(logPdfConfig.getLogMsgSub().toArray(new String[logPdfConfig.getLogMsgSub().size()]));
                    JsonResultVo<List<Map<String, String>>> listJsonResultVo1 = attachFileService.sysErrorTimeConsumingAnalysis(sysErrorDTO);
                    List<Map<String, String>> mapListsysErrorTimeConsumingAnalysis = listJsonResultVo1.getData();
                    pdfInputDTO.setMapListsysErrorTimeConsumingAnalysis(mapListsysErrorTimeConsumingAnalysis);
//                    4
                    searchHelpAnalyseDto.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    searchHelpAnalyseDto.setPageSize((int) logPdfConfig.getPageSize());
                    searchHelpAnalyseDto.setStartTime(logInputDTO.getStartTime());
                    searchHelpAnalyseDto.setEndTime(logInputDTO.getEndTime());
                    searchHelpAnalyseDto.setLimit(logPdfConfig.getLimit());
                    JsonResultVo<List<Map<String, String>>> listJsonResultVo2 = attachFileService.searchHelpAnalysis(searchHelpAnalyseDto);
                    List<Map<String, String>> mapListsearchHelpAnalysis = listJsonResultVo2.getData();
                    pdfInputDTO.setMapListsearchHelpAnalysis(mapListsearchHelpAnalysis);
//                    5
                    gateWayLogDTO02.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    gateWayLogDTO02.setPageSize((int) logPdfConfig.getPageSize());
                    gateWayLogDTO02.setStartTime(logInputDTO.getStartTime());
                    gateWayLogDTO02.setEndTime(logInputDTO.getEndTime());
                    JsonResultVo<QmPage<SearchVO>> qmPageJsonResultVo1 = attachFileService.searchQueryTime(gateWayLogDTO02);
                    QmPage<SearchVO> searchVOQmPage = qmPageJsonResultVo1.getData();
                    List<SearchVO> searchVOListsearchQueryTime = searchVOQmPage.getItems();
                    pdfInputDTO.setSearchVOListsearchQueryTime(searchVOListsearchQueryTime);
//                    6
                    slowSqlLogDTO.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    slowSqlLogDTO.setPageSize((int) logPdfConfig.getPageSize());
                    slowSqlLogDTO.setStartDateTime(logInputDTO.getStartTime());
                    slowSqlLogDTO.setEndtDateTime(logInputDTO.getEndTime());
                    slowSqlLogDTO.setTsortby(logPdfConfig.getTsortbyslow());
                    JsonResultVo<QmPage<SlowSqlLog>> qmPageJsonResultVo2 = attachFileService.slowSqlLogQuery(slowSqlLogDTO);
                    List<SlowSqlLog> slowSqlLogList = new ArrayList<>();
                    if (!BootAppUtil.isNullOrEmpty(qmPageJsonResultVo2)) {
                        QmPage<SlowSqlLog> slowSqlLogQmPage = qmPageJsonResultVo2.getData();
                        slowSqlLogList = slowSqlLogQmPage.getItems();
                    }
                    pdfInputDTO.setSlowSqlLogList(slowSqlLogList);
//                    7
                    dto01.setCurrentPage((int) logPdfConfig.getCurrentPage());
                    dto01.setPageSize((int) logPdfConfig.getPageSize());
                    dto01.setStartTime(logInputDTO.getStartTime());
                    dto01.setEndTime(logInputDTO.getEndTime());
                    dto01.setStatusList(logPdfConfig.getStatusList());
                    JsonResultVo<QmPage<QuartzStatisticsVO>> qmPageJsonResultVo3 = attachFileService.executionStatisticsQuery(dto01);
                    QmPage<QuartzStatisticsVO> quartzStatisticsVOQmPage = qmPageJsonResultVo3.getData();
                    List<QuartzStatisticsVO> quartzStatisticsVOStatusList01 = quartzStatisticsVOQmPage.getItems();
                    pdfInputDTO.setQuartzStatisticsVOStatusList01(quartzStatisticsVOStatusList01);
//                    8
                    List<String> stringList = new ArrayList<>();
                    dto01.setStatusList(stringList);
                    JsonResultVo<QmPage<QuartzStatisticsVO>> qmPageJsonResultVo4 = attachFileService.executionStatisticsQuery(dto01);
                    QmPage<QuartzStatisticsVO> quartzStatisticsVOQmPage01 = qmPageJsonResultVo4.getData();
                    List<QuartzStatisticsVO> quartzStatisticsVOStatusList02 = quartzStatisticsVOQmPage01.getItems();
                    pdfInputDTO.setQuartzStatisticsVOStatusList02(quartzStatisticsVOStatusList02);

                    ByteArrayOutputStream byteArrayOutputStream = getOutputStreamUsePDF(pdfInputDTO);
                    String pdfFileName = nbillId + "_" + logPdfConfig.getPdfname() + "_" + logPdfConfig.getPdfev() + ".pdf";
/**                    pdfDocumentCreateNew(byteArrayOutputStream, pdfFileName);
 *                    resultVo = 1;
 */
                    MultipartFileDecorator mfd = new MultipartFileDecorator(byteArrayOutputStream.toByteArray(), pdfFileName, pdfFileName);
                    AttachFileDO fileDO = attachFileService.upload(mfd, nbillId, localLog, "0");
                    if (!BootAppUtil.isNullOrEmpty(fileDO)) {
                        attachFileCenterVOJsonResultVo = epSysRemote.getAttachByBustype(BootAppUtil.getLoginKey().getTenantId(), attachImputItemDTO);
                        attachFileCenterVO = attachFileCenterVOJsonResultVo.getData();
                    }
                }
            }
        } catch (Exception ex) {
            log.info("---error--"+"导出统一日志导出PDF文件主流程异常，" + ex.getMessage(), ex);
        }
        return attachFileCenterVO;
    }

    @SuppressWarnings("squid:S3776")
    public void generateEpTestPdf(Document document, PdfInputDTO pdfInputDTO) {
        String endTime = (pdfInputDTO.getStartTime().substring(0, 10) + pdfInputDTO.getEndTime().substring(0, 10)).replace("-", "");
        String titlePdf = endTime + "-" + logPdfConfig.getPdfname() + "-" + logPdfConfig.getPdfev();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            Paragraph paragraph = new Paragraph(titlePdf, titlefont);
            paragraph.setAlignment(0); //设置文字居中 0靠左   1，居中     2，靠右
            paragraph.setIndentationLeft(0); //设置左缩进
            paragraph.setIndentationRight(12); //设置右缩进
            paragraph.setFirstLineIndent(0); //设置首行缩进
            paragraph.setLeading(20f); //行间距
            paragraph.setSpacingBefore(5f); //设置段落上空白
            paragraph.setSpacingAfter(5f); //设置段落下空白

            PdfPTable tablePerson = pdfUtil.createTableFree(new float[]{40, 80, 120, 80}, maxWidth);
            tablePerson.addCell(pdfUtil.createCell("由" + logPdfConfig.getPdfcreateman() + "创建", textfontGay, Element.ALIGN_LEFT, 4, false));
            tablePerson.addCell(pdfUtil.createCell("点检人", textfont, Element.ALIGN_CENTER));
            tablePerson.addCell(pdfUtil.createCell("点检时间", textfont, Element.ALIGN_CENTER));
            tablePerson.addCell(pdfUtil.createCell("统计周期", textfont, Element.ALIGN_CENTER));
            tablePerson.addCell(pdfUtil.createCell("系统环境", textfont, Element.ALIGN_CENTER));

            tablePerson.addCell(pdfUtil.createCell(logPdfConfig.getPdfcreateman(), textfont));
            tablePerson.addCell(pdfUtil.createCell(DateUtils.getSysdateStr(DATE_TIME_PATTERN), textfont));
            tablePerson.addCell(pdfUtil.createCell(pdfInputDTO.getStartTime() + "~" + pdfInputDTO.getEndTime(), textfont));
            tablePerson.addCell(pdfUtil.createCell(logPdfConfig.getPdfev(), textfont));

            Chunk chunk = new Chunk("1、接口性能点检清单", headfontGay);
            Chunk chunk1 = new Chunk("1.1、平均耗时Top10", keyfontGay);

            PdfPTable table1 = pdfUtil.createTableFree(new float[]{2, 8, 20, 5, 10, 5, 5, 5, 5}, maxWidth);
            String ltMsg = i18nUtil.getMessage("MSG.sysres.pdfFileImpl.localTitle");
            table1.addCell(pdfUtil.createCell(ltMsg, textfontGay, Element.ALIGN_LEFT, 9, false));
            table1.addCell(pdfUtil.createCell("", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("服务名", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("接口URI", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("成功率", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("平均耗时(ms)", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("执行数量(次)", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table1.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));

            if (!pdfInputDTO.getMapListgatewayTimeConsumingAnalysis().isEmpty()) {
                int i = 1;
                for (Map<String, String> tempMap : pdfInputDTO.getMapListgatewayTimeConsumingAnalysis()) {
                    table1.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table1.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("serviceName")) ? "" : tempMap.get("serviceName"), textfont));
                    table1.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("url")) ? "" : tempMap.get("url"), textfont));
                    table1.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("successRate")) ? "0.0%" : tempMap.get("successRate") + "%", textfont));
                    table1.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("ms")) ? "" : tempMap.get("ms"), textfont));
                    table1.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("num")) ? "" : tempMap.get("num"), textfont));
                    table1.addCell(pdfUtil.createCell("", textfont));
                    table1.addCell(pdfUtil.createCell("", textfont));
                    table1.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }

            } else {
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
                table1.addCell(pdfUtil.createCell("", textfont));
            }

            Chunk chunk2 = new Chunk("1.2、耗时Top10", keyfontGay);
            PdfPTable table2 = pdfUtil.createTableFree(new float[]{2, 8, 8, 20, 5, 5, 5, 5, 5}, maxWidth);
            table2.addCell(pdfUtil.createCell(ltMsg, textfontGay, Element.ALIGN_LEFT, 9, false));
            table2.addCell(pdfUtil.createCell("", keyfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("执行时间", textfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("服务名", textfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("接口URI", textfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("执行耗时(ms)", textfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("Traceid", textfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table2.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));

            if (!pdfInputDTO.getGateWayLogListgatewayLogsQuery().isEmpty()) {
                int i = 1;
                for (GateWayLog gateWayLogTemp : pdfInputDTO.getGateWayLogListgatewayLogsQuery()) {
                    table2.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table2.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(gateWayLogTemp.getAccessTime()) ? "" : sdf.format(gateWayLogTemp.getAccessTime()), textfont));
                    table2.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(gateWayLogTemp.getServiceName()) ? "" : gateWayLogTemp.getServiceName(), textfont));
                    table2.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(gateWayLogTemp.getUri()) ? "" : gateWayLogTemp.getUri(), textfont));
                    table2.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(gateWayLogTemp.getTimeConsuming()) ? "" : gateWayLogTemp.getTimeConsuming(), textfont));
                    table2.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(gateWayLogTemp.getTraceId()) ? "" : gateWayLogTemp.getTraceId(), textfont));
                    table2.addCell(pdfUtil.createCell("", textfont));
                    table2.addCell(pdfUtil.createCell("", textfont));
                    table2.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }
            } else {
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
                table2.addCell(pdfUtil.createCell("", textfont));
            }
            Chunk chunk3 = new Chunk("1.3、服务异常Top10", keyfontGay);
            PdfPTable table3 = pdfUtil.createTableFree(new float[]{2, 8, 20, 10, 5, 5, 5,}, maxWidth);
            table3.addCell(pdfUtil.createCell(ltMsg, textfontGay, Element.ALIGN_LEFT, 7, false));
            table3.addCell(pdfUtil.createCell("", keyfont, Element.ALIGN_CENTER));
            table3.addCell(pdfUtil.createCell("服务名", textfont, Element.ALIGN_CENTER));
            table3.addCell(pdfUtil.createCell("异常信息", textfont, Element.ALIGN_CENTER));
            table3.addCell(pdfUtil.createCell("异常数量", textfont, Element.ALIGN_CENTER));
            table3.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table3.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table3.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));
            if (!pdfInputDTO.getMapListsysErrorTimeConsumingAnalysis().isEmpty()) {
                int i = 1;
                for (Map<String, String> tempMap : pdfInputDTO.getMapListsysErrorTimeConsumingAnalysis()) {
                    table3.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table3.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("type")) ? "" : tempMap.get("type"), textfont));
                    table3.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("logMsg")) ? "" : tempMap.get("logMsg"), textfont));
                    table3.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get(localCount)) ? "" : tempMap.get(localCount), textfont));
                    table3.addCell(pdfUtil.createCell("", textfont));
                    table3.addCell(pdfUtil.createCell("", textfont));
                    table3.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }
            } else {
                table3.addCell(pdfUtil.createCell("", textfont));
                table3.addCell(pdfUtil.createCell("", textfont));
                table3.addCell(pdfUtil.createCell("", textfont));
                table3.addCell(pdfUtil.createCell("", textfont));
                table3.addCell(pdfUtil.createCell("", textfont));
                table3.addCell(pdfUtil.createCell("", textfont));
                table3.addCell(pdfUtil.createCell("", textfont));
            }
            Chunk chunk4 = new Chunk("2、搜索帮助性能点检清单", headfontGay);
            Chunk chunk5 = new Chunk("2.1、平均耗时Top10", keyfontGay);

            PdfPTable table4 = pdfUtil.createTableFree(new float[]{2, 10, 8, 8, 5, 5, 5}, maxWidth);
            table4.addCell(pdfUtil.createCell(ltMsg, textfontGay, Element.ALIGN_LEFT, 7, false));
            table4.addCell(pdfUtil.createCell("", keyfont, Element.ALIGN_CENTER));
            table4.addCell(pdfUtil.createCell("搜索帮助名称", textfont, Element.ALIGN_CENTER));
            table4.addCell(pdfUtil.createCell("平均耗时(ms)", textfont, Element.ALIGN_CENTER));
            table4.addCell(pdfUtil.createCell("执行数量(次)", textfont, Element.ALIGN_CENTER));
            table4.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table4.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table4.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));
            if (!pdfInputDTO.getMapListsearchHelpAnalysis().isEmpty()) {
                int i = 1;
                for (Map<String, String> tempMap : pdfInputDTO.getMapListsearchHelpAnalysis()) {
                    table4.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table4.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("name")) ? "" : tempMap.get("name"), textfont));
                    table4.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get("ms")) ? "" : tempMap.get("ms"), textfont));
                    table4.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.get(localCount)) ? "" : tempMap.get(localCount), textfont));
                    table4.addCell(pdfUtil.createCell("", textfont));
                    table4.addCell(pdfUtil.createCell("", textfont));
                    table4.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }
            } else {
                table4.addCell(pdfUtil.createCell("", textfont));
                table4.addCell(pdfUtil.createCell("", textfont));
                table4.addCell(pdfUtil.createCell("", textfont));
                table4.addCell(pdfUtil.createCell("", textfont));
                table4.addCell(pdfUtil.createCell("", textfont));
                table4.addCell(pdfUtil.createCell("", textfont));
                table4.addCell(pdfUtil.createCell("", textfont));
            }
            Chunk chunk6 = new Chunk("2.2、耗时Top10", keyfontGay);
            PdfPTable table5 = pdfUtil.createTableFree(new float[]{2, 8, 8, 5, 8, 5, 5, 5}, maxWidth);
            table5.addCell(pdfUtil.createCell(ltMsg, textfontGay, Element.ALIGN_LEFT, 8, false));
            table5.addCell(pdfUtil.createCell("", keyfont, Element.ALIGN_CENTER));
            table5.addCell(pdfUtil.createCell("执行时间", textfont, Element.ALIGN_CENTER));
            table5.addCell(pdfUtil.createCell("搜索帮助名称", textfont, Element.ALIGN_CENTER));
            table5.addCell(pdfUtil.createCell("执行时耗(ms)", textfont, Element.ALIGN_CENTER));
            table5.addCell(pdfUtil.createCell("traceId", textfont, Element.ALIGN_CENTER));
            table5.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table5.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table5.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));
            if (!pdfInputDTO.getSearchVOListsearchQueryTime().isEmpty()) {
                int i = 1;
                for (SearchVO tempMap : pdfInputDTO.getSearchVOListsearchQueryTime()) {
                    table5.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table5.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getAccessTime()) ? "" : sdf.format(tempMap.getAccessTime()), textfont));
                    table5.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getName()) ? "" : tempMap.getName(), textfont));
                    table5.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getTimeConsuming()) ? "" : tempMap.getTimeConsuming(), textfont));
                    table5.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getTraceId()) ? "" : tempMap.getTraceId(), textfont));
                    table5.addCell(pdfUtil.createCell("", textfont));
                    table5.addCell(pdfUtil.createCell("", textfont));
                    table5.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }
            } else {
                table5.addCell(pdfUtil.createCell("", textfont));
                table5.addCell(pdfUtil.createCell("", textfont));
                table5.addCell(pdfUtil.createCell("", textfont));
                table5.addCell(pdfUtil.createCell("", textfont));
                table5.addCell(pdfUtil.createCell("", textfont));
                table5.addCell(pdfUtil.createCell("", textfont));
                table5.addCell(pdfUtil.createCell("", textfont));
                table5.addCell(pdfUtil.createCell("", textfont));
            }
            Chunk chunk7 = new Chunk("3、数据库执行性能点检清单", headfontGay);
            Chunk chunk8 = new Chunk("3.1、慢Sql Top10", keyfontGay);
            PdfPTable table6 = pdfUtil.createTableFree(new float[]{2, 8, 5, 5, 5, 2, 8, 10, 2, 2, 2}, maxWidth);
            table6.addCell(pdfUtil.createCell("统计数据来源:数据库慢SQl日志,《EP系统-统一日志-慢SQL日志》用例", textfontGay, Element.ALIGN_LEFT, 11, false));
            table6.addCell(pdfUtil.createCell("", keyfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("执行时间", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("执行时耗(ms)", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("客户端IP", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("数据库地址", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("数据库实例", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("返回记录数", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("SQL", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table6.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));
            if (!pdfInputDTO.getSlowSqlLogList().isEmpty()) {
                int i = 1;
                for (SlowSqlLog tempMap : pdfInputDTO.getSlowSqlLogList()) {
                    table6.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table6.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getSqlTimestamp()) ? "" : sdf.format(new Date(tempMap.getSqlTimestamp())), textfont));
                    table6.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getQueryTime()) ? "" : String.valueOf(tempMap.getQueryTime()), textfont));
                    table6.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getClientIp()) ? "" : tempMap.getClientIp(), textfont));
                    table6.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getDbIP()) ? "" : tempMap.getDbIP(), textfont));
                    table6.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getDbDatabase()) ? "" : tempMap.getDbDatabase(), textfont));
                    table6.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getRowsSent()) ? "" : String.valueOf(tempMap.getRowsSent()), textfont));
                    table6.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getSql()) ? "" : tempMap.getSql(), textfont));
                    table6.addCell(pdfUtil.createCell("", textfont));
                    table6.addCell(pdfUtil.createCell("", textfont));
                    table6.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }
            } else {
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
                table6.addCell(pdfUtil.createCell("", textfont));
            }
            Chunk chunk9 = new Chunk("4、定时任务", headfontGay);
            Chunk chunk10 = new Chunk("4.1、异常统计Top10", keyfontGay);
            PdfPTable table7 = pdfUtil.createTableFree(new float[]{2, 20, 8, 5, 5, 5}, maxWidth);
            table7.addCell(pdfUtil.createCell("统计数据来源:《EP系统-统一日志-定时任务日志》", textfontGay, Element.ALIGN_LEFT, 6, false));
            table7.addCell(pdfUtil.createCell("", textfont, Element.ALIGN_CENTER));
            table7.addCell(pdfUtil.createCell("名称", textfont, Element.ALIGN_CENTER));
            table7.addCell(pdfUtil.createCell("失败次数", textfont, Element.ALIGN_CENTER));
            table7.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table7.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table7.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));

            if (!pdfInputDTO.getQuartzStatisticsVOStatusList01().isEmpty()) {
                int i = 1;
                for (QuartzStatisticsVO tempMap : pdfInputDTO.getQuartzStatisticsVOStatusList01()) {
                    table7.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table7.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getJobName()) ? "" : tempMap.getJobName(), textfont));
                    table7.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getNqty()) ? "" : tempMap.getNqty(), textfont));
                    table7.addCell(pdfUtil.createCell("", textfont));
                    table7.addCell(pdfUtil.createCell("", textfont));
                    table7.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }
            } else {
                table7.addCell(pdfUtil.createCell("", textfont));
                table7.addCell(pdfUtil.createCell("", textfont));
                table7.addCell(pdfUtil.createCell("", textfont));
                table7.addCell(pdfUtil.createCell("", textfont));
                table7.addCell(pdfUtil.createCell("", textfont));
                table7.addCell(pdfUtil.createCell("", textfont));
            }
            Chunk chunk11 = new Chunk("4.2、执行统计Top10", keyfontGay);
            PdfPTable table8 = pdfUtil.createTableFree(new float[]{2, 20, 8, 5, 5, 5}, maxWidth);
            table8.addCell(pdfUtil.createCell("统计数据来源:《EP系统-统一日志-定时任务日志》", textfontGay, Element.ALIGN_LEFT, 6, false));
            table8.addCell(pdfUtil.createCell("", textfont, Element.ALIGN_CENTER));
            table8.addCell(pdfUtil.createCell("名称", textfont, Element.ALIGN_CENTER));
            table8.addCell(pdfUtil.createCell("执行次数", textfont, Element.ALIGN_CENTER));
            table8.addCell(pdfUtil.createCell("处理人", textfont, Element.ALIGN_CENTER));
            table8.addCell(pdfUtil.createCell("处理时间", textfont, Element.ALIGN_CENTER));
            table8.addCell(pdfUtil.createCell("备注", textfont, Element.ALIGN_CENTER));

            if (!pdfInputDTO.getQuartzStatisticsVOStatusList02().isEmpty()) {
                int i = 1;
                for (QuartzStatisticsVO tempMap : pdfInputDTO.getQuartzStatisticsVOStatusList02()) {
                    table8.addCell(pdfUtil.createCell(String.valueOf(i), textfont));
                    table8.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getJobName()) ? "" : tempMap.getJobName(), textfont));
                    table8.addCell(pdfUtil.createCell(BootAppUtil.isNullOrEmpty(tempMap.getNqty()) ? "" : tempMap.getNqty(), textfont));
                    table8.addCell(pdfUtil.createCell("", textfont));
                    table8.addCell(pdfUtil.createCell("", textfont));
                    table8.addCell(pdfUtil.createCell("", textfont));
                    i++;
                }

            } else {
                table8.addCell(pdfUtil.createCell("", textfont));
                table8.addCell(pdfUtil.createCell("", textfont));
                table8.addCell(pdfUtil.createCell("", textfont));
                table8.addCell(pdfUtil.createCell("", textfont));
                table8.addCell(pdfUtil.createCell("", textfont));
                table8.addCell(pdfUtil.createCell("", textfont));
            }

            document.add(paragraph);
            document.add(tablePerson);
            document.add(chunk);
            document.add(Chunk.NEWLINE); // 新建一行
            document.add(chunk1);
            document.add(table1);
            document.add(chunk2);
            document.add(table2);
            document.add(chunk3);
            document.add(table3);
            document.add(chunk4);
            document.add(Chunk.NEWLINE); // 新建一行
            document.add(chunk5);
            document.add(table4);
            document.add(chunk6);
            document.add(table5);
            document.add(chunk7);
            document.add(Chunk.NEWLINE); // 新建一行
            document.add(chunk8);
            document.add(table6);
            document.add(chunk9);
            document.add(Chunk.NEWLINE); // 新建一行
            document.add(chunk10);
            document.add(table7);
            document.add(chunk11);
            document.add(table8);
        } catch (Exception ex) {
            log.info("---error--"+"导出统一日志导出PDF文件-组织pdf文件，" + ex.getMessage(), ex);
        }
    }

    public void pdfDocumentCreateNew(ByteArrayOutputStream byteArrayOutputStream, String fileName) {
        try {
            File file = new File("E:\\project\\program_Code\\IDEAProject\\threeOne\\File\\" + fileName);
            if (file.createNewFile()) {
                FileOutputStream fos2 = new FileOutputStream(file);
                byteArrayOutputStream.writeTo(fos2);
                fos2.close();
            }
        } catch (Exception ex) {
            log.info("---error--"+"导出统一日志导出PDF文件-生成文件，" + ex.getMessage(), ex);
        }

    }
}
