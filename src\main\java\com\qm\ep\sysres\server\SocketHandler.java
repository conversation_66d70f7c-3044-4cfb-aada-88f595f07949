package com.qm.ep.sysres.server;

import com.qm.ep.sysres.constant.SocketConstant;
import com.qm.tds.api.listener.EpRedisListerer;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 监听消息(采用redis发布订阅方式发送消息)
 */
@Slf4j
@Component
public class SocketHandler implements EpRedisListerer {

    @Autowired
    private WebSocketCServer webSocket;

    @Override
    public void onMessage(Map map) {
        log.info("【SocketHandler消息】Redis Listerer:" + map.toString());

        String userId = map.get("userId").toString();
        String message = map.get("message").toString();
        if (BootAppUtil.isnotNullOrEmpty(userId)) {
            webSocket.pushMessage(userId, message);
            //app端消息推送
            webSocket.pushMessage(userId + SocketConstant.APP_SESSION_SUFFIX, message);
        } else {
            webSocket.pushMessage(message);
        }
    }
}