package com.qm.ep.sysres.utils;

import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;

/**
 * iText操作工具类
 *
 * <AUTHOR>
 * @date 20210902
 */
@Slf4j
public class Watermark extends PdfPageEventHelper {
    Font font = new Font(Font.FontFamily.HELVETICA, 30, Font.BOLD, new GrayColor(0.95f));
    private String waterCont;//水印内容

    public Watermark() {
        // Do nothing because of X and Y.
    }

    public Watermark(String waterCont) {
        this.waterCont = waterCont;
    }

    @Override
    public void onEndPage(PdfWriter writer, Document document) {
        try {
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font titlefont = new Font(bfChinese, 30, Font.BOLD, new GrayColor(0.95f));
            for (int i = 0; i < 5; i++) {
                for (int j = 0; j < 5; j++) {
                    ColumnText.showTextAligned(writer.getDirectContentUnder(),
                            Element.ALIGN_CENTER,
                            new Phrase(waterCont == null ? "HELLO WORLD" : waterCont, titlefont),
                            (50.5f + i * 350),
                            (40.0f + j * 150),
                            writer.getPageNumber() % 2 == 1 ? 45 : -45);
                }
            }
        } catch (Exception e) {
            log.info("---error--"+ e.getMessage());
        }
    }
}