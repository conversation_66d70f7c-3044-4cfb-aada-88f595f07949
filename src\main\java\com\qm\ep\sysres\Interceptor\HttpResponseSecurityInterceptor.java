package com.qm.ep.sysres.Interceptor;

import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/7
 */
public class HttpResponseSecurityInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //互联网上的资源有各种类型，通常浏览器会根据响应头的Content-Type字段来分辨它们的类型。通过这个响应头可以禁用浏览器的类型猜测行为
        response.setHeader("X-Content-Type-Options", "nosniff");
        //1; mode=block：启用XSS保护，并在检查到XSS攻击时，停止渲染页面
        response.setHeader("X-XSS-Protection","1; mode=block");
        //这个响应头主要是用来定义页面可以加载哪些资源，减少XSS的发生
        response.setHeader("Content-Security-Policy","default-src 'self'");
        //简称为HSTS。它允许一个HTTPS网站，要求浏览器总是通过HTTPS来访问它
        response.setHeader("strict-transport-security","max-age=16070400; includeSubDomains");

        response.setHeader("Referrer-Policy", "no-referrer-when-downgrade");
        // 用于指定当不能将“crossdomain.xml”文件
        // （当需要从别的域名中的某个文件中读取Flash内容时用于进行必要设置的策略文件）放置在网站根目录等场合时采取的替代策略。
        //  master-only只允许使用主策略文件（/crossdomain.xml）
        response.setHeader("X-Permitted-Cross-Domain-Policies", "master-only");

        // noopen 用于指定IE 8以上版本的用户不打开文件而直接保存文件。在下载对话框中不显示“打开”选项。
        response.setHeader("X-Download-Options", "noopen");

        // 如果从script或stylesheet读入的文件的MIME类型与指定MIME类型不匹配，不允许读取该文件。用于防止XSS等跨站脚本攻击
        response.setHeader("X-Frame-Options", "nosniff");


        return true;
    }
}
