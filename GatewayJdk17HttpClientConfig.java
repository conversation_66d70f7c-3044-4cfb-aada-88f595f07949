package com.qm.cloud.gateway.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.gateway.config.HttpClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Gateway服务JDK17兼容性HTTP客户端配置
 * 解决JDK17下的连接问题和超时问题
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Configuration
@ConfigurationProperties(prefix = "app.gateway.jdk17")
@Slf4j
public class GatewayJdk17HttpClientConfig {

    private boolean enabled = true;
    private ConnectionPoolConfig connectionPool = new ConnectionPoolConfig();
    private TimeoutConfig timeout = new TimeoutConfig();
    private RetryConfig retry = new RetryConfig();

    /**
     * JDK17兼容的HTTP客户端配置
     */
    @Bean
    public HttpClientCustomizer jdk17HttpClientCustomizer() {
        return httpClient -> {
            if (!enabled) {
                log.info("JDK17兼容模式未启用，使用默认配置");
                return httpClient;
            }

            log.info("启用JDK17兼容的HTTP客户端配置");
            
            return httpClient
                // JDK17兼容性：连接超时配置
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, (int) timeout.getConnect().toMillis())
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.TCP_NODELAY, true)
                
                // JDK17兼容性：响应超时配置
                .responseTimeout(timeout.getResponse())
                
                // JDK17兼容性：读写超时处理
                .doOnConnected(conn -> 
                    conn.addHandlerLast(new ReadTimeoutHandler(timeout.getRead().toSeconds(), TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(timeout.getWrite().toSeconds(), TimeUnit.SECONDS)))
                
                // JDK17兼容性：连接池配置
                .connectionProvider(createJdk17ConnectionProvider())
                
                // JDK17兼容性：错误处理
                .doOnError((request, throwable) -> {
                    log.error("---error--HTTP客户端请求失败，URL: {}, 错误: {}", 
                        request.uri(), throwable.getMessage(), throwable);
                }, (response, throwable) -> {
                    log.error("---error--HTTP客户端响应失败，状态: {}, 错误: {}", 
                        response.status(), throwable.getMessage(), throwable);
                });
        };
    }

    /**
     * 创建JDK17兼容的连接池
     */
    private ConnectionProvider createJdk17ConnectionProvider() {
        return ConnectionProvider.builder("gateway-jdk17-pool")
            .maxConnections(connectionPool.getMaxConnections())
            .maxIdleTime(connectionPool.getMaxIdleTime())
            .maxLifeTime(connectionPool.getMaxLifeTime())
            .pendingAcquireTimeout(connectionPool.getAcquireTimeout())
            .evictInBackground(connectionPool.getEvictInterval())
            // JDK17兼容性：启用连接池指标
            .metrics(true)
            .build();
    }

    // 配置类
    public static class ConnectionPoolConfig {
        private int maxConnections = 500;
        private Duration maxIdleTime = Duration.ofSeconds(30);
        private Duration maxLifeTime = Duration.ofMinutes(5);
        private Duration acquireTimeout = Duration.ofSeconds(30);
        private Duration evictInterval = Duration.ofSeconds(120);

        // Getters and Setters
        public int getMaxConnections() { return maxConnections; }
        public void setMaxConnections(int maxConnections) { this.maxConnections = maxConnections; }
        
        public Duration getMaxIdleTime() { return maxIdleTime; }
        public void setMaxIdleTime(Duration maxIdleTime) { this.maxIdleTime = maxIdleTime; }
        
        public Duration getMaxLifeTime() { return maxLifeTime; }
        public void setMaxLifeTime(Duration maxLifeTime) { this.maxLifeTime = maxLifeTime; }
        
        public Duration getAcquireTimeout() { return acquireTimeout; }
        public void setAcquireTimeout(Duration acquireTimeout) { this.acquireTimeout = acquireTimeout; }
        
        public Duration getEvictInterval() { return evictInterval; }
        public void setEvictInterval(Duration evictInterval) { this.evictInterval = evictInterval; }
    }

    public static class TimeoutConfig {
        private Duration connect = Duration.ofSeconds(30);
        private Duration response = Duration.ofSeconds(600);
        private Duration read = Duration.ofSeconds(600);
        private Duration write = Duration.ofSeconds(60);

        // Getters and Setters
        public Duration getConnect() { return connect; }
        public void setConnect(Duration connect) { this.connect = connect; }
        
        public Duration getResponse() { return response; }
        public void setResponse(Duration response) { this.response = response; }
        
        public Duration getRead() { return read; }
        public void setRead(Duration read) { this.read = read; }
        
        public Duration getWrite() { return write; }
        public void setWrite(Duration write) { this.write = write; }
    }

    public static class RetryConfig {
        private int maxAttempts = 2;
        private Duration initialBackoff = Duration.ofSeconds(2);
        private Duration maxBackoff = Duration.ofSeconds(10);
        private double multiplier = 2.0;

        // Getters and Setters
        public int getMaxAttempts() { return maxAttempts; }
        public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }
        
        public Duration getInitialBackoff() { return initialBackoff; }
        public void setInitialBackoff(Duration initialBackoff) { this.initialBackoff = initialBackoff; }
        
        public Duration getMaxBackoff() { return maxBackoff; }
        public void setMaxBackoff(Duration maxBackoff) { this.maxBackoff = maxBackoff; }
        
        public double getMultiplier() { return multiplier; }
        public void setMultiplier(double multiplier) { this.multiplier = multiplier; }
    }

    // Main class getters and setters
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public ConnectionPoolConfig getConnectionPool() { return connectionPool; }
    public void setConnectionPool(ConnectionPoolConfig connectionPool) { this.connectionPool = connectionPool; }
    
    public TimeoutConfig getTimeout() { return timeout; }
    public void setTimeout(TimeoutConfig timeout) { this.timeout = timeout; }
    
    public RetryConfig getRetry() { return retry; }
    public void setRetry(RetryConfig retry) { this.retry = retry; }
}
