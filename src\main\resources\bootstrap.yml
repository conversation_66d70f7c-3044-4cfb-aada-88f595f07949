spring:
  profiles:
    active: local #指定激活哪个环境配置，激活后，第一个文档内容失效;不指定时，以第一个文档为准
  application:
    name: tds-service-sys-res
server:
  port: 8339
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles:
    on-profile: dev
  # 配置中心
  cloud:
    config:
      uri: http://localhost:7005/
      fail-fast: true
      name: ${spring.application.name}
      profile: ${spring.profiles.active}
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles:
    on-profile: test
  # 配置中心
  cloud:
    config:
      uri: http://localhost:7005/
      fail-fast: true
      name: ${spring.application.name}
      profile: ${spring.profiles.active}
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles:
    on-profile: nacos
  # 配置中心
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ************:8848
      config:
        # 配置中心地址
        server-addr: ************:8848
        namespace: ep-dev
        # 组名也需要加上
        group: TDS_EP_NACOS_GROUP
        #配置文件类型，目前只支持 properties 和 yaml 类型，默认为 properties
        file-extension: yaml
        shared-configs[0]:
          # 配置文件名-Data Id
          data-id: global-config.yml
          # 默认为DEFAULT_GROUP
          group: TDS_EP_NACOS_GROUP
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles:
    on-profile: local
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
  cloud:
    nacos:
      config:
        server-addr: localhost:8848
      discovery:
        server-addr: localhost:8848
  mvc:
    async:
      request-timeout: 600000ms    # JDK8兼容性：增加到10分钟
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 10MB    # JDK8兼容性：文件阈值
      resolve-lazily: true         # JDK8兼容性：延迟解析
  datasource:
    dynamic:
      primary: tenant
      strict: true
      datasource:
        master:
          url: ********************************************************************************************************************************************
          username: 385c92f3-2ba8-4fe8-8fc7-bc32ebdcd3cd
          password: lkJfDesqTFfIwTVB
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        tenant:
          url: ****************************************************************************************************************************************************
          username: 385c92f3-2ba8-4fe8-8fc7-bc32ebdcd3cd
          password: lkJfDesqTFfIwTVB
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        w:
          url: ********************************************************************************************************************************************
          username: 385c92f3-2ba8-4fe8-8fc7-bc32ebdcd3cd
          password: lkJfDesqTFfIwTVB
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        r:
          url: ********************************************************************************************************************************************
          username: 385c92f3-2ba8-4fe8-8fc7-bc32ebdcd3cd
          password: lkJfDesqTFfIwTVB
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      druid:
        connectionErrorRetryAttempts: 0
        breakAfterAcquireFailure: true
        initialSize: 5
        maxActive: 100
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 30000
        validationQuery: select 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        removeAbandoned: true
        removeAbandonedTimeout: 1800
        poolPreparedStatements: false
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  rabbitmq:
    host: *************
    port: 5672
    username: admin
    password: mingyue123
    virtual-host: /
    listener:
      simple:
        retry:
          max-attempts: 5
          enabled: true
          initial-interval: 5000
          max-interval: 100000
          multiplier: 1
        default-requeue-rejected: false
        acknowledge-mode: manual
mybatis-plus:
  mapper-locations:
    - classpath*:mapper/tds/dynamic/*Mapper.xml
    - classpath*:mapper/tds/base/*Mapper.xml
    - classpath*:mapper/*Mapper.xml
qm:
  websocket:
    number: ${random.int(10000)}
    mqSendFlag: true
  ftp:
    limit: 10
    logServerName: TDSZUUL
    tsortby: timeConsuming|descend
    tsortbyslow: queryTime|descend
    currentPage: 1
    pageSize: 10
    logMsgSub:
      - kanban
    statusList:
      - 500
      - E
    title: Title@PDF-Java
    author: Author@liuyong
    subject: Subject@iText pdf sample
    keywords: Keywords@iTextpdf
    creator: Creator@liuyong`s
    watermark: 启明EP性能检测
    pdfname: 性能点检表-红旗EP
    pdfcreateman: 陶磊
    pdfev: 净月测试
  base:
    download: new
    upload:
      # 1 FTP,2 腾讯COS, 3 TDSv2文件服务器， 默认ftp
      store-type: 4
      cos-secretId: JEPKQNUDIMSJHRA41NPT
      cos-secretKey: f0iaGX87IY6i502FyVlOWTQOXHmjVTQQtIFWdXgJ
      cos-regionName: obs.cn-northeast-204.faw.com.cn
      cos-bucket: uat-sa-0201-002
      bucket-path: tdsfile
      tdsv2:
        url: 'https://hqtds-uat.faw.cn/epfileservice/TDSFileService.asmx?wsdl'
        #url: 'https://miw-policy-uat.faw.cn/wsdl.html'
ep:
  sysres:
    export:
      easyexcel: true
tupload:
  filePath: /app/JCK-TEST/upload_tmp/uploadFile
  localTempPath: /app/JCK-TEST/upload_tmp
# JDK17兼容性：HTTP客户端配置
http:
  client:
    max-total: 2700
    default-max-per-route: 100
    connect-timeout: 30
    connection-request-timeout: 30
    response-timeout: 600  # 增加到600秒支持大数据导出
    retry-count: 3
    retry-interval: 1
    keep-alive-time: 5

## 应用超时配置
#app:
#  timeout:
#    http:
#      connect: 30          # HTTP连接超时（秒）
#      connection-request: 30  # 连接请求超时（秒）
#      response: 600        # HTTP响应超时（秒）
#    export:
#      small: 300           # 小数据量导出超时（秒）
#      medium: 600          # 中等数据量导出超时（秒）
#      large: 900           # 大数据量导出超时（秒）
#      extra-large: 1800    # 超大数据量导出超时（秒）
#    retry:
#      max-attempts: 2      # 最大重试次数
#      initial-delay: 2000  # 初始重试延迟（毫秒）
#      multiplier: 1.5      # 重试延迟倍数
#      max-delay: 10000     # 最大重试延迟（毫秒）