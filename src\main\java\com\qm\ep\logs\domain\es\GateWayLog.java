package com.qm.ep.logs.domain.es;


import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: es 日志实体
 * @author: Cyl
 * @time: 2020/07/29 12:55
 */
@Data
public class GateWayLog implements Serializable {
    private static final long serialVersionUID = 2812584588681449823L;

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 端口
     */
    private Integer port;

    /**
     * ip
     */
    @JsonAlias("IP")
    private String ip;


    /**
     * 接口地址
     */
    @JsonAlias("URI")
    private String uri;

    /**
     * 入参
     */
    @JsonAlias("ENTRY_PARAMETERS")
    private String inParams;

    /**
     * 出参
     */
    @JsonAlias("OUT_PARAMETERS")
    private String outParams;

    /**
     * 消耗时间
     */
    @JsonAlias("TIME_CONSUMING")
    private String timeConsuming;

    /**
     * 本机地址
     */
    private String host;

    /**
     * 状态
     */
    @JsonAlias("STATUS")
    private Integer status;

    /**
     * 日志级别
     */
    private String level;

    /**
     * TraceId
     */
    private String traceId;

    /**
     * SpanId
     */
    private String spanId;

    /**
     * 请求类型
     */
    @JsonAlias("HTTP_METHOD")
    private String httpMethod;

    /**
     * 请求服务名称
     */
    @JsonAlias("SERVICE_NAME")
    private String serviceName;


    /**
     * 自定义名称
     */
    @JsonAlias("LOG_SERVER_NAME")
    private String logServerName;

    /**
     * 请求内容类型
     */
    @JsonAlias("REQUEST_CONTENT_TYPE")
    private String requestContentType;

    /**
     * 响应类型
     */
    @JsonAlias("RESPONSE_CONTENT_TYPE")
    private String responseContentType;

    /**
     * 访问时间
     */
    @JsonAlias("ACCESS_TIME")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accessTime;

    /**
     * 登录信息
     */
    @JsonAlias("HEADER_INFO")
    private LoginKeyLog loginKey;

}
