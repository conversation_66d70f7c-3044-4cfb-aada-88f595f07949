package com.qm.ep.sysres.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.qm.ep.sysres.domain.bean.AttachFileDO;
import com.qm.ep.sysres.domain.bean.SreTableExportLogDO;
import com.qm.ep.sysres.domain.dto.ICAPPersonInfoOutDTO;
import com.qm.ep.sysres.domain.dto.MessageSendNoticeDTO;
import com.qm.ep.sysres.remote.EpCommonUIRemote;
import com.qm.ep.sysres.remote.EpSysRemote;
import com.qm.ep.sysres.remote.NoticeRemote;
import com.qm.ep.sysres.service.AttachFileService;
import com.qm.ep.sysres.service.ITableService;
import com.qm.ep.sysres.utils.HttpClientExceptionHandler;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.base.domain.MultipartFileDecorator;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
public class TableServiceImpl implements ITableService {
    /**
     * 用于导出调用业务模块
     */
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private DiscoveryClient discoveryClient;
    @Autowired
    private EpCommonUIRemote epCommonUIRemote;
    @Autowired
    private NoticeRemote noticeRemote;
    @Autowired
    private AttachFileService attachFileService;
    @Autowired
    private EpSysRemote epSysRemote;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private HttpClientExceptionHandler httpClientExceptionHandler;

    @Value("${ep.sysres.export.gateway.servicename:cloud-gateway-service}")
    private String gateName;

    /**
     * 导出Excel时，每次获取的最大记录数
     */
    @Value("${ep.sysres.export.pagesize:800}")
    private int pageSize;
    /**
     * 导出Excel时，最多获取页数。
     * 即最大记录条数为 pageSize*pageLimit
     * 20230112因个别业务导出量较大做出调整，由最多200页改为300页，总条数由160000万变240000
     * 如果影响内存，则改回来！！
     */
    @Value("${ep.sysres.export.pagelimit:300}")
    private int pageLimit;
    /**
     * 导出Excel时，是否记录日志与导出Excel文件，以便后续可以继续下载。
     * 默认true。
     */
    @Value("${ep.sysres.export.log:true}")
    private boolean needLog;

    /**
     * 导出Excel时，生成文件是否需要进行压缩
     */
    @Value("${ep.sysres.export.logcompress:false}")
    private boolean needLogCompress;

    /**
     * 生成文件超过多长时间（秒）才发送系统消息
     */
    @Value("${ep.sysres.export.msgtimewait:60}")
    private int logMsgTimeWait;

    /**
     * 生成消息类型：以下这个为净月环境合法id。不同的环境可能不同，获取方式请联系系统管理员。
     */
    @Value("${ep.sysres.export.msgtypeid:25026514305741d2a5379181dba9dc70}")
    private String logMsgTypeId;


    /* 返回结果中的KEY */
    private static final String RET_KEY_CODE = "code";
    private static final String RET_KEY_ERRMSG = "errMsg";
    private static final String RET_KEY_DATA = "data";
    private static final String RET_KEY_FILE_NAME = "fileName";
    private static final String RET_KEY_PAGE_INDEX = "currentPage";
    private static final String RET_KEY_PAGE_TOTAL = "totalPages";
    private static final String RET_KEY_ITEM_TOTAL = "total";

    private static final String WIDTH = "width";
    private static final String SHEETNAME = "sheetName";
    private static final String SHEETDATA = "sheetData";

    /* 请求参数中的KEY */
    private static final String PARA_KEY_TRANS_CODE = "vtranscode";
    private static final String PARA_KEY_MENU_NAME = "vmenuname";
    private static final String PARA_KEY_FILE_NAME = "fileName";
    private static final String PARA_KEY_PAGE_SIZE = "pageSize";
    private static final String PARA_KEY_PAGE_INDEX = "currentPage";
    private static final String PARA_KEY_SERVICE_NAME = "serviceName";
    private static final String PARA_KEY_MULTI_HEADER_DATA = "multiHeaderData";

    private static final String PARA_KEY_SHEETS = "sheets";
    private static final String PARA_KEY_INCLUDE_HEADER = "includeHeader";
    private static final String PARA_KEY_COLUMN_STYLE = "columnStyle";

    /**
     * 记录导出日志
     *
     * @param exportLogDO 导出日志
     * @return 保存后日志对象
     */
    private void saveExportLog(SreTableExportLogDO exportLogDO) {
        if (needLog) {
            JsonResultVo<SreTableExportLogDO> ret = epCommonUIRemote.tableExportLogSave(exportLogDO);
            if (!ret.isOk()) {
                log.info("---error--"+"保存Excel导出日志报错！" + ret.getMsg());
            } else {
                SreTableExportLogDO tmp = ret.getData();
                // 将保存后的ID和时间戳更新到do中，以便可以持续更新
                exportLogDO.setId(tmp.getId());
                exportLogDO.setDtstamp(tmp.getDtstamp());
            }
        }
    }

    /**
     * 导出开始日志
     *
     * @param exportLogDO 导出日志
     * @param exportInfo  导出Excel请求参数
     * @return 保存后日志对象
     */
    private void saveExportInit(SreTableExportLogDO exportLogDO, Map<String, Object> exportInfo) {
        try {
            exportLogDO.setVtraceid(MDC.get("traceId"));
        } catch (Exception e) {
            log.info("---error--"+"记录导出Excel日志traceid失败！" + e.getMessage(), e);
        }
        try {
            String transCode = exportInfo.get(PARA_KEY_TRANS_CODE) == null ? "" : exportInfo.get(PARA_KEY_TRANS_CODE).toString();
            String menuName = exportInfo.get(PARA_KEY_MENU_NAME) == null ? "" : exportInfo.get(PARA_KEY_MENU_NAME).toString();
            exportLogDO.setVtranscode(transCode);
            exportLogDO.setVmenuname(menuName);
            exportLogDO.setVpara(getExportPara(exportInfo).toJSONString());

            LoginKeyDO loginKeyDO = BootAppUtil.getLoginKey();
            exportLogDO.setNopr(loginKeyDO.getOperatorId());
            exportLogDO.setVopr(loginKeyDO.getPersonCode());
            exportLogDO.setVoprname(loginKeyDO.getOperatorName());
            exportLogDO.setNcompanyid(loginKeyDO.getCompanyId());

            exportLogDO.setDbegin(DateUtils.getSysdateTime());
            exportLogDO.setDpulse(DateUtils.getSysdateTime());
            exportLogDO.setNprocess(2);
            exportLogDO.setDtstamp(DateUtils.getSysTimestamp());

            //导出文件名称----20220614
            String fileName = exportInfo.get(RET_KEY_FILE_NAME) == null ? "" : exportInfo.get(RET_KEY_FILE_NAME).toString();
            log.info("-----fileName-----"+fileName);
            exportLogDO.setVfilename(fileName);

            saveExportLog(exportLogDO);
        } catch (Exception ex) {
            log.warn("记录导出Excel日志失败saveExportInit！" + ex.getMessage(), ex);
        }
    }

    private void saveExportProcess(SreTableExportLogDO exportLogDO, Object pageIndex, Object pageTotal, int dataSize) {
        try {
            int nProcess = exportLogDO.getNprocess();
            int nPageIndex = 0;
            int nPageTotal = 0;
            if (pageIndex != null) {
                nPageIndex = Integer.parseInt(pageIndex.toString());
            }
            /**
             * 只有第一页才有pagetotal信息，其他时候从缓存中获取。
             * 即如果入参中没有，则再从日志对象中获取一次。
             * add by wjq on 20211014
             */
            if (pageTotal != null) {
                nPageTotal = Integer.parseInt(pageTotal.toString());
                if (nPageTotal > 0) {
                    exportLogDO.setNrecordpage(nPageTotal);
                }
            }
            if (nPageTotal == 0) {
                nPageTotal = exportLogDO.getNrecordpage();
            }
            if (nPageTotal == 0 && exportLogDO.getNTotalSize() != 0 && exportLogDO.getNPageSize() != 0) {
                nPageTotal = (int) Math.ceil(exportLogDO.getNTotalSize() / (float) exportLogDO.getNPageSize());//需要线程数
            }
            if (nPageTotal > 0) {
                /**
                 * nProcess += 1 / nPageTotal;
                 */
                nProcess = 2 + 90 * nPageIndex / nPageTotal;
            }
            exportLogDO.setNrecordsize(exportLogDO.getNrecordsize() + dataSize);
            exportLogDO.setDpulse(DateUtils.getSysdateTime());
            exportLogDO.setNprocess(nProcess);
            saveExportLog(exportLogDO);
        } catch (Exception ex) {
            log.warn("记录导出Excel日志失败saveExportProcess！" + ex.getMessage(), ex);
        }
    }

    private void saveExportProcess(SreTableExportLogDO exportLogDO, int process) {
        try {
            exportLogDO.setDpulse(DateUtils.getSysdateTime());
            exportLogDO.setNprocess(exportLogDO.getNprocess() + process);
            saveExportLog(exportLogDO);
        } catch (Exception ex) {
            log.warn("记录导出Excel日志失败saveExportProcess！" + ex.getMessage(), ex);
        }
    }

    private void saveExportEnd(SreTableExportLogDO exportLogDO, String fileId, int fileSize) {
        try {
            exportLogDO.setDpulse(DateUtils.getSysdateTime());
            exportLogDO.setDend(DateUtils.getSysdateTime());
            exportLogDO.setNprocess(100);
            exportLogDO.setVfileid(fileId);
            exportLogDO.setNfilesize(fileSize);
            saveExportLog(exportLogDO);
        } catch (Exception ex) {
            log.warn("记录导出Excel日志失败saveExportEnd！" + ex.getMessage(), ex);
        }
    }

    /**
     * 分页获取接口数据，防止数据量太大时一次性读取导致业务服务内存溢出。
     *
     * @param exportLogDO 日志信息
     * @param url         数据接口URL
     * @param exportInfo  导出Excel请求参数
     * @param pageIndex   当前分页页码
     * @return 分页接口数据
     */
    @SuppressWarnings("squid:S3776")
    private List<Map<String, Object>> getRemoteDataByPages(SreTableExportLogDO exportLogDO, String url, Map<String, Object> exportInfo, int pageIndex) {
        List<Map<String, Object>> datas = new ArrayList<>();
        JsonResultVo<?> jsonResultObj;
        long s1 = System.currentTimeMillis();

        log.debug("执行doExport 分页获取数据开始，index:{}，size:{}，serviceName：{}", pageIndex, pageSize, url);
        HttpEntity<String> entity = getHttpEntity(exportInfo, pageIndex, pageSize);

        try {
            // JDK17兼容性：使用重试机制处理连接问题
            jsonResultObj = executeWithRetry(url, entity, pageIndex);
        } catch (Exception e) {
            // 使用专门的异常处理器处理JDK17兼容性问题
            String errorMessage = httpClientExceptionHandler.handleHttpException(e, url, pageIndex);
            String retryAdvice = httpClientExceptionHandler.getRetryAdvice(e);
            log.error("---error--{} | {}", errorMessage, retryAdvice);

            // 创建包含详细信息的异常
            throw httpClientExceptionHandler.createJdk17CompatibilityException(e, url, pageIndex);
        }

        log.debug("-------请求数据耗时(毫秒)  :" + (System.currentTimeMillis() - s1) + "。jsonResultObj：" + jsonResultObj);
        if (jsonResultObj != null) {
            if (jsonResultObj.getStatus().equals("E")) {
                // 调用数据接口异常，中断导出动作
                String message = i18nUtil.getMessage("ERR.sysres.tableService.getPageFail");
                String errMsg = message + jsonResultObj.getMsg();
                log.info("---error--"+errMsg);
                throw new QmException(errMsg);
            }
            LinkedHashMap jsondatas = (LinkedHashMap) jsonResultObj.getData();
            log.debug("执行doExport 执行数据解析，currentPage：{}，totalPages：{}，mount：{}", jsondatas.get(RET_KEY_PAGE_INDEX), jsondatas.get(RET_KEY_PAGE_TOTAL), jsondatas.get(RET_KEY_ITEM_TOTAL));
            List<LinkedHashMap> items = (List<LinkedHashMap>) jsondatas.get("items");
            if (CollectionUtils.isEmpty(items)) {
                items = (List<LinkedHashMap>) jsondatas.get("records");
            }
            if (items != null) {
                for (LinkedHashMap jsondata : items) {
                    // map对象
                    Map<String, Object> data = new HashMap<>();
                    // 循环转换
                    Iterator it = jsondata.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<String, Object> entry = (Map.Entry<String, Object>) it.next();
                        data.put(entry.getKey(), entry.getValue());
                    }
                    datas.add(data);
                }
            }
            if (!BootAppUtil.isNullOrEmpty(jsondatas.get(RET_KEY_ITEM_TOTAL)))
                exportLogDO.setNTotalSize(Integer.parseInt(jsondatas.get(RET_KEY_ITEM_TOTAL).toString()));
            if (!BootAppUtil.isNullOrEmpty(jsondatas.get(PARA_KEY_PAGE_SIZE)))
                exportLogDO.setNPageSize(Integer.parseInt(jsondatas.get(PARA_KEY_PAGE_SIZE).toString()));
            // 记录导出日志
            saveExportProcess(exportLogDO, pageIndex, jsondatas.get(RET_KEY_PAGE_TOTAL), datas.size());
        }
        log.debug("执行doExport 分页获取数据结束，存在返回数据，执行数据解析，转载data：" + datas.size() + "，耗时(毫秒) " + (System.currentTimeMillis() - s1));
        return datas;
    }

    /**
     * 分页获取数据并导出Excel
     *
     * @param exportInfo 导出Excel请求参数
     * @return 导出Excel信息
     */
    @Override
    public Map<String, Object> doExport(Map<String, Object> exportInfo) {
        Map<String, Object> outMap = new HashMap<>();
        List<ServiceInstance> serviceInstances = discoveryClient.getInstances(gateName);
        if (CollectionUtils.isEmpty(serviceInstances)) {
            outMap.put(RET_KEY_CODE, "0");
            String message = i18nUtil.getMessage("MSG.sysres.tableService.serviceNotFound", gateName);
            outMap.put(RET_KEY_ERRMSG, message);
            return outMap;
        }
        int sIndex = new Random().nextInt(serviceInstances.size());
        String serverUri = "http://" + serviceInstances.get(sIndex).getHost() + ":" + serviceInstances.get(sIndex).getPort();
        //TODO serverUri可能会不通，在consul中会存在无效的节点信息（尤其是刚刚发布后。consul的心跳机制可能有问题）

        log.debug("执行doExport serverUri：" + serverUri);
        Map<String, Object> headerMap = new HashMap<>();
        if (exportInfo.get(PARA_KEY_MULTI_HEADER_DATA) != null) {
            headerMap = JSONUtils.jsonToMap(exportInfo.get(PARA_KEY_MULTI_HEADER_DATA).toString());
        }

        SreTableExportLogDO exportLogDO = new SreTableExportLogDO();
        try {
            //记录导出Excel日志
            saveExportInit(exportLogDO, exportInfo);

            // 获取数据的服务 serviceName=common-demo/table/select
            String serviceName = exportInfo.get(PARA_KEY_SERVICE_NAME).toString();
            if (serviceName.startsWith("/api")) {
                serviceName = serviceName.substring(4);
            }

            List<Map<String, Object>> datas = new ArrayList<>();
            //循环获取数据
            for (int i = 1; i <= pageLimit; i++) {
                List<Map<String, Object>> tmpList = getRemoteDataByPages(exportLogDO, serverUri + serviceName, exportInfo, i);
                datas.addAll(tmpList);
                if (tmpList.size() < pageSize) {
                    // 已经是最后一页了，中断取数动作
                    break;
                }
            }

            // 导出文件名称
            String fileName = exportInfo.get(PARA_KEY_FILE_NAME).toString();
            log.debug("执行doExport 生成Excel开始，fileName：" + fileName);
            //判断是否是复杂表头----20221031----

            ByteArrayOutputStream outPut = new ByteArrayOutputStream();
            int easyFlag = 0;
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            if(headerMap.size() == 0){
                //EasyExcel, 组装数据
                HashMap outPutMap = easyExportData(datas, exportInfo, fileName, headerMap);
                datas.clear();
                List<Map> resData = (List<Map>) outPutMap.get("excelData");
                List<List<Object>> res = (List<List<Object>>) resData.get(0).get(SHEETDATA);
                EasyExcel.write(stream).sheet("sheet").doWrite(res);
                easyFlag = 1;
                outMap.put(RET_KEY_DATA, stream);
                outMap.put(RET_KEY_FILE_NAME, fileName);
            }else {
                //复杂表头，还是用原来的生成模式
                outPut = exportData(datas, exportInfo, fileName, headerMap);
                datas.clear();
                outMap.put(RET_KEY_DATA, outPut);
                outMap.put(RET_KEY_FILE_NAME, fileName);
            }
            log.debug("执行doExport 生成Excel结束，装载数据fileName：" + fileName);

            // 上传Excel文件
            if (needLog) {
                String logFileName;
                byte[] logFileBytes;

                if(easyFlag == 1){
                    logFileName = fileName;
                    logFileBytes = stream.toByteArray();

                }else {
                    if (needLogCompress) {
                        //压缩成zip。从效果上看压缩前后的文件大小差异不大。
                        log.debug("执行doExport 生成zip开始...");
                        logFileName = fileName + ".zip";
                        logFileBytes = writeZip(fileName, outPut.toByteArray());
                        log.debug("执行doExport 生成zip结束...");
                        // 记录导出日志
                        saveExportProcess(exportLogDO, 2);
                    } else {
                        logFileName = fileName;
                        logFileBytes = outPut.toByteArray();
                    }
                }
                // 上传文件 这里需要开启新事务，防止超时
                MultipartFileDecorator mfd = new MultipartFileDecorator(logFileBytes, logFileName, logFileName);
                AttachFileDO fileDO = attachFileService.upload(mfd, exportLogDO.getId(), "EXPORT001", "0");
                // 记录导出日志
                saveExportEnd(exportLogDO, fileDO.getId(), logFileBytes.length);
                sendMsg(exportLogDO, fileName);
            }
        } catch (HttpClientErrorException ex) {
            // 使用捕获异常来处理返回的非200状态的不同响应
            String message = ex.getMessage();
            // 获取接口返回状态码
            int statis = ex.getRawStatusCode();
            outMap.put(RET_KEY_CODE, "0");
            outMap.put(RET_KEY_ERRMSG, ex.getMessage());
            log.info("---error--"+"调用业务异常 statis：" + statis + ",:message:" + message, ex);
        } catch (Exception ex) {
            outMap.put(RET_KEY_CODE, "0");
            outMap.put(RET_KEY_ERRMSG, ex.getMessage());
            log.info("---error--"+"调用业务异常 Exceptionmessage:" + ex.getMessage(), ex);
        }
        return outMap;
    }
    @SuppressWarnings({"squid:S3457"})
    public void sendMsg(SreTableExportLogDO exportLogDO, String fileName) {
        //时间>1分钟
        final Calendar cal = Calendar.getInstance();
        cal.setTime(exportLogDO.getDbegin());
        final long time1 = cal.getTimeInMillis();
        cal.setTime(exportLogDO.getDpulse());
        final long time2 = cal.getTimeInMillis();
        final long min = (time2 - time1) / 1000;
        try {
            if (min > logMsgTimeWait) {
                JsonResultVo<String> resultVo;
                String src = "/api/sys-res/attachFile/download?fileId=" + exportLogDO.getVfileid() + "&thumbnailFlag=normal&tenantId=" + BootAppUtil.getLoginKey().getTenantId() + "&downloadFlag=0";
                String href = String.format("<a target='_blank' href=" + src + " >" + fileName + "</a>");
                href = i18nUtil.getMessage("MSG.sysres.tableService.enterDownload", href);
                resultVo = noticeRemote.getIdWorkerSTR("EP");
                String id = resultVo.getData();
                MessageSendNoticeDTO messageSendNoticeDTO = new MessageSendNoticeDTO();
                ICAPPersonInfoOutDTO icapPersonInfoOutDTO = new ICAPPersonInfoOutDTO();
                List<ICAPPersonInfoOutDTO> recievePersonList = new ArrayList<>();
                icapPersonInfoOutDTO.setPersonName(BootAppUtil.getLoginKey().getOperatorName());
                icapPersonInfoOutDTO.setId(BootAppUtil.getLoginKey().getOperatorId());
                icapPersonInfoOutDTO.setAccount(BootAppUtil.getLoginKey().getPersonCode());
                recievePersonList.add(icapPersonInfoOutDTO);
                messageSendNoticeDTO.setId(id);
                messageSendNoticeDTO.setVContext(href);
                messageSendNoticeDTO.setSendPersonCode(BootAppUtil.getLoginKey().getPersonCode());
                messageSendNoticeDTO.setSendPersonId(BootAppUtil.getLoginKey().getOperatorId());
                messageSendNoticeDTO.setSendPersonName(BootAppUtil.getLoginKey().getOperatorName());
                messageSendNoticeDTO.setRecievePersonList(recievePersonList);
                String message = i18nUtil.getMessage("MSG.sysres.tableService.exportFinish");
                messageSendNoticeDTO.setVTitle(fileName + message);
                messageSendNoticeDTO.setVTypeId(logMsgTypeId);
                noticeRemote.saveNoticeByOtherUse(messageSendNoticeDTO);
            }
        } catch (Exception e) {
            log.info("---error--"+"发送系统消息失败！" + e.getMessage(), e);
        }
    }

    @Override
    public void sendInstanceMsg(String fileName) {
        try {
            JsonResultVo<String> resultVo;
            String message = i18nUtil.getMessage("MSG.sysres.tableService.exportFinish");
            resultVo = noticeRemote.getIdWorkerSTR("EP");
            String id = resultVo.getData();
            MessageSendNoticeDTO messageSendNoticeDTO = new MessageSendNoticeDTO();
            ICAPPersonInfoOutDTO icapPersonInfoOutDTO = new ICAPPersonInfoOutDTO();
            List<ICAPPersonInfoOutDTO> recievePersonList = new ArrayList<>();
            icapPersonInfoOutDTO.setPersonName(BootAppUtil.getLoginKey().getOperatorName());
            icapPersonInfoOutDTO.setId(BootAppUtil.getLoginKey().getOperatorId());
            icapPersonInfoOutDTO.setAccount(BootAppUtil.getLoginKey().getPersonCode());
            recievePersonList.add(icapPersonInfoOutDTO);
            messageSendNoticeDTO.setId(id);
            messageSendNoticeDTO.setVContext(fileName + message);
            messageSendNoticeDTO.setSendPersonCode(BootAppUtil.getLoginKey().getPersonCode());
            messageSendNoticeDTO.setSendPersonId(BootAppUtil.getLoginKey().getOperatorId());
            messageSendNoticeDTO.setSendPersonName(BootAppUtil.getLoginKey().getOperatorName());
            messageSendNoticeDTO.setRecievePersonList(recievePersonList);
            messageSendNoticeDTO.setVTitle(fileName + message);
            messageSendNoticeDTO.setVTypeId(logMsgTypeId);
            noticeRemote.saveNoticeByOtherUse(messageSendNoticeDTO);
        } catch (Exception e) {
            log.info("---error--"+"发送系统消息失败！" + e.getMessage(), e);
        }
    }

    /**
     * 获取导出Excel查询条件
     *
     * @param exportInfo 导出Excel请求参数
     * @return 获取数据的查询条件
     */
    private JSONObject getExportPara(Map<String, Object> exportInfo) {
        String[] keys = {"serverUri", "range", PARA_KEY_SERVICE_NAME, PARA_KEY_FILE_NAME, PARA_KEY_INCLUDE_HEADER, PARA_KEY_SHEETS, PARA_KEY_PAGE_INDEX, PARA_KEY_PAGE_SIZE};
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<String, Object> entry : exportInfo.entrySet()) {
            if (!Arrays.asList(keys).contains(entry.getKey())) {
                jsonObject.put(entry.getKey(), entry.getValue());
            }
        }
        return jsonObject;
    }

    private HttpEntity<String> getHttpEntity(Map<String, Object> exportInfo, int pageIndex, int pageSize) {
        HttpHeaders headers = new HttpHeaders();
        // RestTemplate带参传的时候要用HttpEntity<?>对象传递
        JSONObject jsonObject = getExportPara(exportInfo);
        if (pageIndex > 0) {
            jsonObject.put(PARA_KEY_PAGE_INDEX, pageIndex);
        }
        if (pageSize > 0) {
            jsonObject.put(PARA_KEY_PAGE_SIZE, pageSize);
        }
        dealAdditionalParams(jsonObject, headers, pageIndex);
        // 定义请求参数类型，这里用json所以是MediaType.APPLICATION_JSON，否则APPLICATION_FORM_URLENCODED
        // JDK17兼容性：使用APPLICATION_JSON替代已弃用的APPLICATION_JSON_UTF8
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept-Charset", "UTF-8");
        return new HttpEntity<>(jsonObject.toJSONString(), headers);
    }


    /**
     * 表格数据导出的处理
     *
     * @param data
     * @param fileName
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public ByteArrayOutputStream exportData(List<Map<String, Object>> data, Map<String, Object> param, String fileName,
                                            Map<String, Object> headerMap) {
        log.debug("-------进入 exportData:");
        ByteArrayOutputStream output = null;
        try {
            // 导出文件名称
            boolean includeHeader = getMapValue(param, PARA_KEY_INCLUDE_HEADER, true);
            // 导出工作表定义
            List<Map<String, Object>> sheets = getMapValue(param, PARA_KEY_SHEETS, null);
            // 导出工作表（列分组）合计
            if (null != sheets) {
                int sheetCount = sheets.size();
                log.debug("-------exportData sheetCount:" + sheetCount);
                // 各工作表列定义
                List<Map<String, Object>>[] sheetCols = new ArrayList[sheetCount];
                // 各工作表列数量
                int[] sheetColCount = new int[sheetCount];
                // 提取各工作表列定义和列数量
                for (int i = 0; i < sheetCount; i++) {
                    sheetCols[i] = (List<Map<String, Object>>) sheets.get(i).get("sheetColumns");
                    sheetColCount[i] = sheetCols[i].size();
                }
                log.debug("-------exportData :" + fileName);
                List<List<Object>>[] sheetData = new ArrayList[sheetCount];
                List<List<Map<String, Object>>> columnStyleList = new ArrayList();
                // 导出列标题
                if (!includeHeader) {
                    for (int j = 0; j < sheetCount; j++) {
                        sheetData[j] = new ArrayList();
                    }
                } else {
                    sheetData = getExportColumnTitle(sheetCount, sheetColCount, sheetCols, headerMap, columnStyleList);
                }

                // 导出数据
                long s1 = System.currentTimeMillis();
                dealExportData(data, sheetCount, sheetColCount, sheetCols, sheetData);
                log.debug("-------导出数据转换耗时(毫秒)  :" + (System.currentTimeMillis() - s1));
                // 合并到excel格式数据
                List<Map> excelData = new ArrayList();
                for (int i = 0; i < sheetCount; i++) {
                    Map<String, Object> sheet = sheets.get(i);
                    Map<String, Object> xlsSheet = new HashMap<>();

                    xlsSheet.put(SHEETNAME, sheet.get(SHEETNAME).toString());
                    xlsSheet.put(SHEETDATA, sheetData[i]);
                    xlsSheet.put(PARA_KEY_COLUMN_STYLE, columnStyleList.get(i));
                    excelData.add(xlsSheet);
                }
                // 生成Excel
                long s = System.currentTimeMillis();
                log.debug("-------exportData: 执行 writeExcel");
                output = writeExcel(fileName, excelData, headerMap);
                log.debug("-------writeExcel耗时(毫秒) is :" + (System.currentTimeMillis() - s));
            }
        } catch (Exception e) {
            log.info("---error--"+"调用业务异常 exportData:" + e.getMessage(), e);
        }

        return output;
    }

    @SuppressWarnings("all")
    private void dealExportData(List<Map<String, Object>> data, int sheetCount, int[] sheetColCount,
                                List<Map<String, Object>>[] sheetCols, List<List<Object>>[] sheetData) {
        int dataCount = data.size();
        log.debug("-------data num is :" + dataCount);
        for (int i = 0; i < dataCount; i++) {
            Map<String, Object> row = data.get(i);
            for (int j = 0; j < sheetCount; j++) {
                List<Object> line = new ArrayList<>();
                for (int k = 0; k < sheetColCount[j]; k++) {
                    String colName = (String) (sheetCols[j].get(k).get("fieldName"));
                    boolean useDict = (boolean) (sheetCols[j].get(k).get("useDict"));
                    if (colName.equals("pageIndex")) {
                        line.add(i + 1);
                    } else {
                        // 服务端表格导出，处理字典列翻译问题
                        if (useDict) {
                            Map<String, String> refDict = (Map<String, String>) (sheetCols[j].get(k).get("refDict"));
                            if (null != row.get(colName)) {
                                String dictKey = row.get(colName).toString();
                                if (!"".equals(dictKey) && dictKey != null && refDict != null && refDict.size() > 0) {
                                    String dicVal = refDict.get(dictKey);
                                    line.add(dicVal);
                                } else {
                                    line.add(dictKey);
                                }
                            } else
                                line.add("");
                        } else {
                            line.add(row.get(colName));
                        }
                    }
                }
                sheetData[j].add(line);
            }
        }
    }

    private List<List<Object>>[] getExportColumnTitle(int sheetCount, int[] sheetColCount, List<Map<String, Object>>[] sheetCols,
                                                      Map<String, Object> headerMap, List<List<Map<String, Object>>> columnStyleList) {
        // 导出列标题
        List<List<Object>>[] sheetData = new ArrayList[sheetCount];
        for (int j = 0; j < sheetCount; j++) {
            List<Object> line = new ArrayList<>();
            List<Map<String, Object>> columnStyles = new ArrayList<>();
            for (int k = 0; k < sheetColCount[j]; k++) {
                Map<String, Object> columnStyle = new HashMap<>();
                String colLabel = (String) (sheetCols[j].get(k).get("fieldLabel"));
                line.add(colLabel);
                Object widthObj = sheetCols[j].get(k).get(WIDTH);
                String width = "120";
                if (null != widthObj) {
                    width = widthObj.toString();
                }
                Object alignObj = sheetCols[j].get(k).get("align");
                String align = "left";
                if (null != alignObj) {
                    align = alignObj.toString();
                }
                columnStyle.put(WIDTH, width);
                columnStyle.put("align", align);
                columnStyles.add(columnStyle);
            }

            ArrayList arrayList = new ArrayList();

            if (!(headerMap != null && headerMap.size() > 0)) {
                arrayList.add(line);
            }

            sheetData[j] = arrayList;
            columnStyleList.add(columnStyles);
        }
        return sheetData;
    }

    /**
     * 导出数据到excel文件
     *
     * @param fileName  文件名称（含路径）
     * @param excelData 要导出的数据（符合格式要求）
     * @throws Exception
     * @remarks 数据格式如下 [{ "sheetName":"Sheet1", "sheetData":[
     * ["主键","月份","员工编号","员工姓名","部门","岗位","领取时间","基本工资","奖金","工资合计"],
     * [129616,"201204","1","张三","01","经理","2016/09/22
     * 08:43:36",-2500,1500,4000] ]}]
     */
    public ByteArrayOutputStream writeExcel(String fileName, List<Map> excelData, Map<String, Object> headerMap) {
        File excelFile = new File(fileName);
        String excelName = excelFile.getName();
        String excelExtName = excelName.substring(excelName.lastIndexOf('.') + 1);
        try (ByteArrayOutputStream outStream = new ByteArrayOutputStream()) {
            log.debug("执行writeExcel:");
            writeExcel(outStream, excelExtName, excelData, headerMap);
            return outStream;
        } catch (IOException ex) {
            log.info("---error--"+"writeExcel异常：" + ex.getMessage(), ex);
        }
        return null;
    }

    /**
     * 导出数据到excel输出流
     *
     * @param os           输出流
     * @param excelExtName excel文件的扩展名，支持xls和xlsx
     * @param excelData    要导出的数据（符合格式要求）
     */
    public void writeExcel(OutputStream os, String excelExtName, List<Map> excelData, Map<String, Object> headerMap) {
        // 变量声明
        String sheetName;
        double widthRate = 35.7;
        List<List<Object>> sheetData;
        List<List<Map<String, Object>>> sheetColumnStyles;
        Workbook wb = null;
        Sheet sheet;
        int i;
        List data = (List) excelData.get(0).get(SHEETDATA);
        log.debug("执行writeExcel:创建工作表对象");
        try {
            // 创建工作表对象
            wb = getWorkBook(excelExtName, data.size());
            // 遍历工作表
            for (Map sheetInfo : excelData) {
                // 工作表名称
                sheetName = (String) sheetInfo.get(SHEETNAME);
                // 工作表数据
                sheetData = (List<List<Object>>) sheetInfo.get(SHEETDATA);
                sheetColumnStyles = (List<List<Map<String, Object>>>) sheetInfo.get(PARA_KEY_COLUMN_STYLE);
                // 创建工作表
                sheet = wb.createSheet(sheetName);
                // 如果是多表头，这里先写入表头信息
                int m = dealHeaderData(headerMap, wb, sheet);
                for (int p = 0; p < sheetColumnStyles.size(); p++) {
                    Object widthObj = ((Map<String, Object>) sheetColumnStyles.get(p)).get(WIDTH);
                    String width = "120";
                    if (null != widthObj) {
                        width = widthObj.toString();
                    }
                    sheet.setColumnWidth(p, (int) (Integer.parseInt(width) * widthRate));
                }
                // 显示工作表数据
                i = m;
                dealRowData(sheetData, sheet, i);
            }
            wb.write(os);
        } catch (Exception e) {
            log.info("---error--"+"异常 writeExcel:" + e.getMessage(), e);
        }
    }

    private void dealRowData(List<List<Object>> sheetData, Sheet sheet, int i) {
        Row row = null;
        Cell cell = null;
        int j;
        for (List<Object> item : sheetData) {
            // 新行
            row = sheet.createRow(i++);
            j = 0;
            for (Object val : item) {
                // 新格
                cell = row.createCell(j++);
                // 不处理空值
                if (val == null) {
                    continue;
                }
                // 普通单元格
                switch (val.getClass().getName()) {
                    case "java.lang.Integer":
                        // 数值型
                        cell.setCellValue((Integer) val);
                        break;
                    case "java.lang.Double":
                        // 数值型
                        cell.setCellValue((Double) val);
                        break;
                    case "java.lang.Float":
                        // 数值型
                        cell.setCellValue((Float) val);
                        break;
                    case "java.lang.Boolean":
                        // 布尔型
                        cell.setCellValue((Boolean) val);
                        break;
                    default:
                        // 默认按字符型处理
                        cell.setCellValue(val.toString());
                        break;
                }
            }
        }
    }

    private int dealHeaderData(Map<String, Object> headerMap, Workbook wb, Sheet sheet) {
        int m = 0;
        Row row = null;
        Cell cell = null;
        if (headerMap != null && headerMap.size() > 0) {
            // 创建多表头单元格
            CellStyle style = wb.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);// 设置单元格水平居中
            style.setVerticalAlignment(VerticalAlignment.CENTER);// 设置单元格垂直居中
            List<Map<String, Object>> headerDataList = (List<Map<String, Object>>) headerMap.get("multiRowsData");
            for (m = 0; m < headerDataList.size(); m++) {
                row = sheet.createRow(m);
                List<Map<String, Object>> headerDatas = (List<Map<String, Object>>) headerDataList.get(m);
                for (int n = 0; n < headerDatas.size(); n++) {
                    Map<String, Object> headerData = headerDatas.get(n);
                    cell = row.createCell(n);
                    String cellValue = headerData.get("v") == null ? "" : headerData.get("v").toString();
                    cell.setCellValue(cellValue);
                    cell.setCellStyle(style);
                }
            }
            // 多表头单元格合并处理
            List<Map<String, Object>> multiPositionList = (List<Map<String, Object>>) headerMap.get("multiPosition");
            for (int k = 0; k < multiPositionList.size(); k++) {
                Map<String, Object> multiPosition = multiPositionList.get(k);
                Map<String, Object> startPosition = (Map<String, Object>) multiPosition.get("s");
                Map<String, Object> endPosition = (Map<String, Object>) multiPosition.get("e");
                sheet.addMergedRegion(new CellRangeAddress((int) startPosition.get("r"),
                        (int) endPosition.get("r"), (int) startPosition.get("c"), (int) endPosition.get("c")));
            }
        }
        return m;
    }

    private Workbook getWorkBook(String excelExtName, int dataSize) {
        Workbook wb = null;
        if ("xls".equals(excelExtName)) {
            // Excel 2003
            wb = new HSSFWorkbook();
        } else if (dataSize > 5000) {
            wb = new SXSSFWorkbook(1000);
        } else if ("xlsx".equals(excelExtName)) {
            // Excel 2007
            wb = new XSSFWorkbook();
        } else {
            // 错误的扩展名
            throw new QmException("Not valid excel extend file name: " + excelExtName);
        }
        return wb;
    }

    /**
     * 查找模板自定义的分页和排序等表格附加信息
     *
     * @return 表格附加信息。如果没有附加信息则返回null。
     */
    private void dealAdditionalParams(JSONObject jsonObject, HttpHeaders headers, int pageIndex) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == attributes) {
            return;
        }
        HttpServletRequest request = attributes.getRequest();

        // 写入头信息
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                if (name.equals("tsf-metadata")) {
                    continue;
                }
                String value = request.getHeader(name);
                headers.set(name, value);
            }

            headers.set("exportServer", "true");
            //TODO 第一次执行为 true
            if (pageIndex != 1) {
                headers.set("searchCount", "false");
            }
        }
        Enumeration<String> reqParaList = request.getParameterNames();
        while (reqParaList != null && reqParaList.hasMoreElements()) {
            String vParaName = reqParaList.nextElement();
            String value = request.getParameter(vParaName);
            // 表格附加信息
            if (!vParaName.equals("_t")) {
                jsonObject.put(vParaName, value);
            }
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T getMapValue(Map<?, ?> map, Object key, T defaultVal) {
        if (map != null && key != null && map.containsKey(key)) {
            return (T) map.get(key);
        }
        return defaultVal;
    }

    /**
     * 生成Zip文件数据
     *
     * @param fileName   Excel文件名
     * @param excelBytes Excel文件数据
     * @return Zip文件数据
     */
    public byte[] writeZip(String fileName, byte[] excelBytes) {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();ZipOutputStream zipOutputStream = new ZipOutputStream(bos)) {
            ZipEntry entry = new ZipEntry(fileName);
            zipOutputStream.putNextEntry(entry);
            zipOutputStream.write(excelBytes);
            zipOutputStream.closeEntry();
            zipOutputStream.flush();
            bos.flush();
            return bos.toByteArray();
        } catch (IOException ex) {
            log.info("---error--"+"生成Zip文件报错2，" + ex.getMessage(), ex);
        }
        return new byte[0];
    }

    /**
     * 表格数据导出的处理
     *
     * @param data
     * @param fileName
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public HashMap easyExportData(List<Map<String, Object>> data, Map<String, Object> param, String fileName,
                                            Map<String, Object> headerMap) {
        log.debug("-------进入 exportData:");
        HashMap outputMap = new HashMap();
        try {
            // 导出文件名称
            boolean includeHeader = getMapValue(param, PARA_KEY_INCLUDE_HEADER, true);
            // 导出工作表定义
            List<Map<String, Object>> sheets = getMapValue(param, PARA_KEY_SHEETS, null);
            // 导出工作表（列分组）合计
            if (null != sheets) {
                int sheetCount = sheets.size();
                log.debug("-------exportData sheetCount:" + sheetCount);
                // 各工作表列定义
                List<Map<String, Object>>[] sheetCols = new ArrayList[sheetCount];
                // 各工作表列数量
                int[] sheetColCount = new int[sheetCount];
                // 提取各工作表列定义和列数量
                for (int i = 0; i < sheetCount; i++) {
                    sheetCols[i] = (List<Map<String, Object>>) sheets.get(i).get("sheetColumns");
                    sheetColCount[i] = sheetCols[i].size();
                }
                log.debug("-------exportData :" + fileName);
                List<List<Object>>[] sheetData = new ArrayList[sheetCount];
                List<List<Map<String, Object>>> columnStyleList = new ArrayList();
                // 导出列标题
                if (!includeHeader) {
                    for (int j = 0; j < sheetCount; j++) {
                        sheetData[j] = new ArrayList();
                    }
                } else {
                    sheetData = getExportColumnTitle(sheetCount, sheetColCount, sheetCols, headerMap, columnStyleList);
                }

                // 导出数据
                long s1 = System.currentTimeMillis();
                dealExportData(data, sheetCount, sheetColCount, sheetCols, sheetData);
                log.debug("-------导出数据转换耗时(毫秒)  :" + (System.currentTimeMillis() - s1));
                // 合并到excel格式数据
                List<Map> excelData = new ArrayList();
                for (int i = 0; i < sheetCount; i++) {
                    Map<String, Object> sheet = sheets.get(i);
                    Map<String, Object> xlsSheet = new HashMap<>();

                    xlsSheet.put(SHEETNAME, sheet.get(SHEETNAME).toString());
                    xlsSheet.put(SHEETDATA, sheetData[i]);
                    xlsSheet.put(PARA_KEY_COLUMN_STYLE, columnStyleList.get(i));
                    excelData.add(xlsSheet);
                }
                outputMap.put(RET_KEY_FILE_NAME,fileName);
                outputMap.put("excelData",excelData);


            }
        } catch (Exception e) {
            log.info("---error--"+"调用业务异常 exportData:" + e.getMessage(), e);
        }

        return outputMap;
    }

    /**
     * 带重试机制的HTTP请求执行
     * JDK17兼容性：专门处理超时和连接问题
     *
     * @param url 请求URL
     * @param entity 请求实体
     * @param pageIndex 页码
     * @return 响应结果
     */
    private JsonResultVo<?> executeWithRetry(String url, HttpEntity<String> entity, int pageIndex) {
        int maxRetries = 2; // 最多重试2次
        int retryDelay = 2000; // 重试间隔2秒

        for (int attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                log.debug("执行HTTP请求，尝试次数: {}/{}, URL: {}, 页码: {}", attempt, maxRetries + 1, url, pageIndex);
                return restTemplate.postForObject(url, entity, JsonResultVo.class);

            } catch (org.springframework.web.client.ResourceAccessException e) {
                boolean isTimeout = e.getCause() instanceof java.net.SocketTimeoutException;
                boolean isConnectionClosed = e.getMessage() != null &&
                    (e.getMessage().contains("Connection has been closed") ||
                     e.getMessage().contains("insufficient data written"));
                boolean isLastAttempt = attempt > maxRetries;

                if ((isTimeout || isConnectionClosed) && !isLastAttempt) {
                    log.warn("JDK17兼容性问题，准备重试 - 尝试次数: {}/{}, URL: {}, 页码: {}, 错误: {}",
                            attempt, maxRetries + 1, url, pageIndex, e.getMessage());

                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new QmException("重试被中断", ie);
                    }

                    // 增加重试延迟时间
                    retryDelay = Math.min(retryDelay * 2, 10000); // 最大10秒
                    continue;
                } else {
                    // 非JDK17兼容性问题或已达到最大重试次数，直接抛出
                    throw e;
                }
            } catch (Exception e) {
                // 其他异常直接抛出，不重试
                throw e;
            }
        }

        // 理论上不会到达这里
        throw new QmException("HTTP请求执行失败，已达到最大重试次数");
    }
}