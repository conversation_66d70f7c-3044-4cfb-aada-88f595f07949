package com.qm.ep.sysres.controller;


import com.qm.ep.sysres.domain.bean.AttachBusDO;
import com.qm.ep.sysres.domain.bean.AttachFileDO;
import com.qm.ep.sysres.domain.dto.AttachFileDTO;
import com.qm.ep.sysres.domain.vo.AttachCenterFileVO;
import com.qm.ep.sysres.domain.vo.AttachFileCenterVO;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.controller.BaseTestController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.RandomUtils;
import lombok.extern.slf4j.Slf4j;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class AttachFileControllerTest extends BaseTestController<AttachFileController> {
    /**
     * 每个TestCase函数执行前
     */
    @Before
    public void beforMethod() {
        this.initUser(UserConstants.USER_CODE_COMPANY);
    }

    /**
    * <p>Description: 测试实体 </p>
    * Run the Result model()  method test.
    */
	@Test
	public void equalsAndHashCodeContract() {
	    EqualsVerifier.simple().forClass(AttachFileDTO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
	    EqualsVerifier.simple().forClass(AttachFileDO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
        EqualsVerifier.simple().forClass(AttachBusDO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
        EqualsVerifier.simple().forClass(AttachBusDO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
        EqualsVerifier.simple().forClass(AttachFileCenterVO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
        EqualsVerifier.simple().forClass(AttachCenterFileVO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
	}
}
