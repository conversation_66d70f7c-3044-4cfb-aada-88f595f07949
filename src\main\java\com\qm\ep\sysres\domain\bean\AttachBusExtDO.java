package com.qm.ep.sysres.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 业务类型对应的文件扩展名
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysc080d")
@Schema(title = "业务类型对应的文件扩展名", description = "业务类型对应的文件扩展名")
public class AttachBusExtDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "id", description = "id")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(title = "哪个业务场景使用，公司+业务类型唯一", description = "哪个业务场景使用，公司+业务类型唯一")
    @TableField("VBUSTYPE")
    private String vbustype;

    @Schema(title = "文件的扩展名", description = "文件的扩展名")
    @TableField("VEXTENSION")
    private String vextension;

    @Schema(title = "按公司设置，对应公司ID", description = "按公司设置，对应公司ID")
    @TableField("NCO")
    private String nco;

    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;


}
