package com.qm.ep.sysres.config;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.DefaultConnectionKeepAliveStrategy;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;

/**
 * 动态超时配置的RestTemplate
 * 根据请求类型和URL自动调整超时时间
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Component
public class DynamicTimeoutRestTemplate extends RestTemplate {

    public DynamicTimeoutRestTemplate() {
        super(createDynamicTimeoutRequestFactory());
    }

    /**
     * 创建支持动态超时的请求工厂
     */
    private static ClientHttpRequestFactory createDynamicTimeoutRequestFactory() {
        return new DynamicTimeoutClientHttpRequestFactory();
    }

    /**
     * 动态超时的HTTP请求工厂
     */
    private static class DynamicTimeoutClientHttpRequestFactory implements ClientHttpRequestFactory {
        
        private final PoolingHttpClientConnectionManager connectionManager;
        
        public DynamicTimeoutClientHttpRequestFactory() {
            // 配置连接池
            this.connectionManager = new PoolingHttpClientConnectionManager();
            connectionManager.setMaxTotal(2700);
            connectionManager.setDefaultMaxPerRoute(100);
            connectionManager.setValidateAfterInactivity(TimeValue.ofSeconds(30));
        }

        @Override
        public ClientHttpRequest createRequest(URI uri, HttpMethod httpMethod) throws IOException {
            // 根据URI和请求方法确定超时时间
            int responseTimeout = determineResponseTimeout(uri, httpMethod);
            
            // 创建针对此请求的HTTP客户端配置
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(Timeout.ofSeconds(30))
                    .setConnectionRequestTimeout(Timeout.ofSeconds(30))
                    .setResponseTimeout(Timeout.ofSeconds(responseTimeout))
                    .build();

            HttpClient httpClient = HttpClientBuilder.create()
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig)
                    .setRetryStrategy(new DefaultHttpRequestRetryStrategy(3, TimeValue.ofSeconds(1)))
                    .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                    .setUserAgent("TDS-SysRes-Service/1.0-Dynamic")
                    .build();

            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
            factory.setBufferRequestBody(true);
            
            return factory.createRequest(uri, httpMethod);
        }

        /**
         * 根据URI和请求方法确定响应超时时间
         * 
         * @param uri URI
         * @param httpMethod HTTP方法
         * @return 超时时间（秒）
         */
        private int determineResponseTimeout(URI uri, HttpMethod httpMethod) {
            String path = uri.getPath();
            String query = uri.getQuery();
            
            // 导出相关的请求使用更长的超时时间
            if (path != null && (path.contains("export") || path.contains("Export") || 
                                path.contains("searchInvocie") || path.contains("table"))) {
                
                // 检查是否是大数据量请求
                if (query != null && (query.contains("pageSize") || query.contains("export"))) {
                    return 900; // 15分钟，用于大数据导出
                }
                
                return 600; // 10分钟，用于普通导出
            }
            
            // 查询相关的请求
            if (path != null && (path.contains("search") || path.contains("query") || path.contains("list"))) {
                return 300; // 5分钟，用于查询
            }
            
            // 其他请求使用较短超时
            return 120; // 2分钟，用于一般请求
        }
    }
}
