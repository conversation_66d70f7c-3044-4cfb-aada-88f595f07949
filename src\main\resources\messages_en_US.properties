MSG.sysres.common.uploadSuccess=Upload succeeded!
ERR.sysres.common.uploadFail=Upload failed!
MSG.sysres.common.copySuccess=Copy succeeded
MSG.sysres.common.msgSendSuccess=message sending succeeded
MSG.sysres.websocket.setSender=Please specify the sender!
MSG.sysres.pdfFileImpl.localTitle=Statistical data source: use cases in EP system - unified log - interface performance analysis 
ERR.sysres.tableService.getPageFail=doExport paging for data aqucisition failed:
MSG.sysres.tableService.serviceNotFound=%s service not found.
MSG.sysres.tableService.enterDownload=Please click %s to download the file.
MSG.sysres.tableService.exportFinish=Export completed
####################=
ERR.sysres.WebSocketDto.msgNull=Message sending cannot be empty
ERR.sysres.AttachCopyBillVO.originTicketIdNull=Original document id cannot be empty
ERR.sysres.AttachCopyBillVO.newTicketIdNull=New document id cannot be empty
MSG.sysres.WebSocketCServer.mqSendFail=MQ message distribution failed.
MSG.sysres.WebSocketCServer.heartbeatResponse=Heartbeat response
MSG.sysres.PdfFileImpl.commonLogCount=Statistical data source: use cases in EP system - unified log - interface performance analysis
MSG.sysres.PdfFileImpl.createBy=Created by %s
MSG.sysres.PdfFileImpl.checker=Checker
MSG.sysres.PdfFileImpl.checkTime=Check date
MSG.sysres.PdfFileImpl.countPeriod=Statistical period
MSG.sysres.PdfFileImpl.systemEnv=System environment
MSG.sysres.PdfFileImpl.performanceCheckList=1. Interface performance checklist
MSG.sysres.PdfFileImpl.averageCostTimeTop=Average time consuming Top10
MSG.sysres.PdfFileImpl.serverName=Service name
MSG.sysres.PdfFileImpl.interfaceUri=Interface URI
MSG.sysres.PdfFileImpl.successRate=Success rate
MSG.sysres.PdfFileImpl.averageCostTime=Average time (ms)
MSG.sysres.PdfFileImpl.runTimes=Number of executions (times)
MSG.sysres.PdfFileImpl.handler=Processor
MSG.sysres.PdfFileImpl.handleTime=Processing time
MSG.sysres.PdfFileImpl.remarks=Remarks
MSG.sysres.PdfFileImpl.costTimeTop=Time consuming Top10
MSG.sysres.PdfFileImpl.costTime=Execution time
MSG.sysres.PdfFileImpl.costTimems=Execution time (ms)
MSG.sysres.PdfFileImpl.exceptionTop=1.3. Abnormal service Top 10
MSG.sysres.PdfFileImpl.exceptionInfo=Abnormality information
MSG.sysres.PdfFileImpl.exceptionAccount=Number of abnormalities
MSG.sysres.PdfFileImpl.searchHelpPerformanceList=2. Search help performance checklist
MSG.sysres.PdfFileImpl.searchHelpName=Search help name
MSG.sysres.PdfFileImpl.databasePerformanceList=3. Database execution performance checklist
MSG.sysres.PdfFileImpl.showSqlTop=3.1. Slow Sql Top10
MSG.sysres.PdfFileImpl.showSqlCount=Statistical data source: database slow SQL log, use cases in EP system - unified log - slow SQL log 
MSG.sysres.PdfFileImpl.clientIp=Client IP
MSG.sysres.PdfFileImpl.databaseAddress=Database address
MSG.sysres.PdfFileImpl.databaseInstance=Database instance
MSG.sysres.PdfFileImpl.returnRecordCount=Number of records returned
MSG.sysres.PdfFileImpl.quartzTask=4. Timed tasks
MSG.sysres.PdfFileImpl.exceptionCount=4.1. Abnormal statistics Top 10 
MSG.sysres.PdfFileImpl.quartzLogCount=Source of statistical data: EP System - Unified Log - timed Task Log
MSG.sysres.PdfFileImpl.name=Name
MSG.sysres.PdfFileImpl.failTimes=Number of failures
MSG.sysres.PdfFileImpl.execCountTop=4.2. Executive statistics Top 10 
MSG.sysres.PdfFileImpl.execTimes=Number of executions
MSG.sysres.MyHeaderFooter.pageStartEnd=I am header/footer
MSG.sysres.MyHeaderFooter.currentPage=Page %s/
MSG.sysres.MyHeaderFooter.totalPage=Of %s
#############################=
ERR.message.MessageModuleEnum.mobile=Phone number
ERR.message.MessageModuleEnum.emailAddress=Email address
ERR.message.MessageModuleEnum.wxAccount=Wechat number
ERR.message.MessageModuleEnum.dingAccount=DingTalk number
ERR.message.DingTemplateDTO.messageTitleNull=Message title should not be null!
ERR.message.DingTemplateDTO.messageContentNull=Message content should not be null!
ERR.message.DingTemplateDTO.redirectUrlNull=Jump URL should not be null!
ERR.message.DingTemplateDTO.methodContent202=202 ways of content!
ERR.message.MessageTemplateSendDTO.publishDateNull=Release date should not be null
ERR.message.MessageTemplateSendDTO.objectDetailNull=Object details should not be null
ERR.message.MessageTemplateSendUserDTO.busiSysCodeNull=Business system number should not be null
ERR.message.SendDingDTO.dingIdNull=DingTalk user id should not be null!
ERR.message.SendDingEPDTO.epsysIdNull=EP system user id should not be null!
ERR.message.SendDingEPDTO.epsysCompanyIdNull=company ID of the EP system user id!
ERR.message.SendMailDTO.emailReceiverNull=Email recipient address should not be null!
ERR.message.SendSmsDTO.smsReceiverNull=SMS recipient address should not be null!
ERR.message.SendSmsDTO.templateCodeNull=Template ID should not be null!
ERR.message.SendWxTemplateDTO.wxReceiverAddressNull=Wechat recipient address should not be null!
ERR.message.SendWxTemplateDTO.templateContentNull=Template content should not be null!
ERR.message.VerificationDTO.mobileNull=Phone number can not be empty!
ERR.message.MessageFactory.messageFactoryNoTemplate=The message factory does not define a template:
