package com.qm.ep.sysres.remote;

import com.qm.ep.sys.domain.dto.AttachImputItemDTO;
import com.qm.ep.sysres.domain.vo.AttachFileCenterVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 熔断处理
 *
 * <AUTHOR> wjq
 * @date 2021/3/21
 */
@Component
@Slf4j
public class EpSysRemoteHystrix extends QmRemoteHystrix<EpSysRemote> implements EpSysRemote {

    @Override
    public JsonResultVo<AttachFileCenterVO> getAttachByBustype(@RequestHeader("tenantId") String tenantId, @RequestBody AttachImputItemDTO attachImputItemDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> getPersonIdByCode(@RequestBody String personCode) {
        return getResult();
    }

}
