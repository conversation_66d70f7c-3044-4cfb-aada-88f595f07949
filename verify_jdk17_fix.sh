#!/bin/bash

# JDK17兼容性修复验证脚本
# 用于验证修复是否正确应用

echo "=== JDK17兼容性修复验证 ==="
echo "开始时间: $(date)"
echo ""

# 1. 检查JDK版本
echo "1. 检查JDK版本..."
java -version
echo ""

# 2. 编译检查
echo "2. 编译检查..."
if mvn clean compile -q; then
    echo "✅ 编译成功"
else
    echo "❌ 编译失败"
    exit 1
fi
echo ""

# 3. 运行单元测试
echo "3. 运行JDK17兼容性相关测试..."
if mvn test -Dtest=HttpClientConfigTest -q; then
    echo "✅ HTTP客户端配置测试通过"
else
    echo "⚠️  HTTP客户端配置测试失败（可能需要调整）"
fi

if mvn test -Dtest=HttpClientExceptionHandlerTest -q; then
    echo "✅ 异常处理器测试通过"
else
    echo "⚠️  异常处理器测试失败（可能需要调整）"
fi
echo ""

# 4. 检查关键配置文件
echo "4. 检查关键配置..."

# 检查HTTP客户端配置
if grep -q "setBufferRequestBody(true)" src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java; then
    echo "✅ 找到关键的setBufferRequestBody配置"
else
    echo "❌ 缺少setBufferRequestBody配置"
fi

# 检查超时配置
if grep -q "Timeout.ofSeconds(30)" src/main/java/com/qm/ep/sysres/EpSysResServiceIscApplication.java; then
    echo "✅ 找到优化的超时配置"
else
    echo "❌ 缺少优化的超时配置"
fi

# 检查媒体类型修复
if grep -q "MediaType.APPLICATION_JSON)" src/main/java/com/qm/ep/sysres/service/impl/TableServiceImpl.java; then
    echo "✅ 找到修复的媒体类型配置"
else
    echo "❌ 缺少媒体类型修复"
fi

# 检查配置文件
if grep -q "http:" src/main/resources/bootstrap.yml; then
    echo "✅ 找到HTTP客户端配置项"
else
    echo "❌ 缺少HTTP客户端配置项"
fi
echo ""

# 5. 检查新增的文件
echo "5. 检查新增的文件..."
files=(
    "src/main/java/com/qm/ep/sysres/config/HttpClientConfig.java"
    "src/main/java/com/qm/ep/sysres/config/Jdk17HttpClientConfig.java"
    "src/main/java/com/qm/ep/sysres/utils/HttpClientExceptionHandler.java"
    "JDK17_COMPATIBILITY_FIX.md"
    "DEPLOYMENT_GUIDE.md"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
    fi
done
echo ""

# 6. 打包测试
echo "6. 打包测试..."
if mvn clean package -DskipTests -q; then
    echo "✅ 打包成功"
    
    # 检查生成的JAR文件
    jar_file=$(find target -name "tds-service-sys-res-*.jar" | head -1)
    if [ -f "$jar_file" ]; then
        echo "✅ JAR文件生成: $jar_file"
        echo "   文件大小: $(du -h "$jar_file" | cut -f1)"
    else
        echo "❌ JAR文件未生成"
    fi
else
    echo "❌ 打包失败"
fi
echo ""

# 7. 生成修复报告
echo "7. 生成修复报告..."
cat > jdk17_fix_report.txt << EOF
JDK17兼容性修复报告
生成时间: $(date)

修复的主要问题:
1. insufficient data written - 通过setBufferRequestBody(true)解决
2. Connection has been closed - 通过优化超时配置解决
3. 媒体类型过时 - 替换APPLICATION_JSON_UTF8

关键修改文件:
- EpSysResServiceIscApplication.java: HTTP客户端配置优化
- TableServiceImpl.java: 媒体类型修复和异常处理
- bootstrap.yml: 添加HTTP客户端配置项

新增文件:
- HttpClientConfig.java: 可配置的HTTP客户端
- Jdk17HttpClientConfig.java: 简化的JDK17兼容配置
- HttpClientExceptionHandler.java: 专门的异常处理器

测试文件:
- HttpClientConfigTest.java: 配置测试
- HttpClientExceptionHandlerTest.java: 异常处理测试
- TableExportJdk17Test.java: 集成测试

部署建议:
1. 先在测试环境验证
2. 监控HTTP连接异常频率
3. 根据实际情况调整超时参数
4. 关注内存使用情况

EOF

echo "✅ 修复报告已生成: jdk17_fix_report.txt"
echo ""

echo "=== 验证完成 ==="
echo "结束时间: $(date)"
echo ""
echo "下一步操作:"
echo "1. 在测试环境部署并验证"
echo "2. 测试/table/doExport接口功能"
echo "3. 监控日志中是否还有JDK17兼容性错误"
echo "4. 根据实际情况调整配置参数"
