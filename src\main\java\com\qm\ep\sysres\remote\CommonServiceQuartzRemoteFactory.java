package com.qm.ep.sysres.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class CommonServiceQuartzRemoteFactory implements FallbackFactory<CommonServiceQuartzRemote> {
    @Override
    public CommonServiceQuartzRemote create(Throwable throwable) {
        CommonServiceQuartzRemoteHystrix hystrix = new CommonServiceQuartzRemoteHystrix();
        hystrix.setHystrixEx(throwable);
        return hystrix;
    }
}
