package com.qm.ep.sysres.remote;

import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Repository
@FeignClient(name = "common-service-quartz", fallbackFactory = EpSysRemoteFactory.class)
public interface CommonServiceQuartzRemote {

    @ApiOperation(value = "执行统计")
    @PostMapping("/quartz/executionStatistics")
    JsonResultVo executionStatistics(@RequestBody QuartzDTO dto);

    @ApiOperation(value = "待分页执行统计")
    @PostMapping("/quartz/executionStatisticsQuery")
    JsonResultVo<QmPage<QuartzStatisticsVO>> executionStatisticsQuery(@RequestBody QuartzDTO dto);
}
