package com.qm.ep.sysres.remote;

import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Repository
@FeignClient(name = "common-service-quartz", fallbackFactory = EpSysRemoteFactory.class)
public interface CommonServiceQuartzRemote {

    @Operation(summary = "执行统计", description = "执行统计[author:10027705]")
    @PostMapping("/quartz/executionStatistics")
    JsonResultVo executionStatistics(@RequestBody QuartzDTO dto);

    @Operation(summary = "待分页执行统计", description = "待分页执行统计[author:10027705]")
    @PostMapping("/quartz/executionStatisticsQuery")
    JsonResultVo<QmPage<QuartzStatisticsVO>> executionStatisticsQuery(@RequestBody QuartzDTO dto);
}
