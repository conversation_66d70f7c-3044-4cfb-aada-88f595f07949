package com.qm.ep.sysres.remote;

import com.qm.ep.sysres.domain.bean.SreTableExportLogDO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@Slf4j
public class EpCommonUIRemoteHystrix extends QmRemoteHystrix<EpCommonUIRemote> implements EpCommonUIRemote {
    @Override
    public JsonResultVo<SreTableExportLogDO> tableExportLogSave(@RequestBody SreTableExportLogDO tempDO) {
        return getResult();
    }
}
