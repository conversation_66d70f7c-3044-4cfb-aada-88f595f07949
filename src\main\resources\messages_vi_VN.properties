#这里填写越南语翻译
MSG.sysres.common.uploadSuccess=Tải lên thành công!
ERR.sysres.common.uploadFail=Tải lên thất bại!
MSG.sysres.common.copySuccess=copy thành công
MSG.sysres.common.msgSendSuccess=Gửi tin nhắn thành công
MSG.sysres.websocket.setSender=Hãy chỉ định người gửi!
MSG.sysres.pdfFileImpl.localTitle=Nguồn dữ liệu thống kê: Trường hợp sử dụng "Hệ thống EP - nhật ký thống nhất - phân tích hiệu suất giao diện"
ERR.sysres.tableService.getPageFail=Thực hiện phân trang doExport  nhận dữ liệu thất bại:
MSG.sysres.tableService.serviceNotFound=Chưa phát hiện dịch vụ %s.
MSG.sysres.tableService.enterDownload=<PERSON><PERSON><PERSON> nhấp vào %s để tải xuống tệp.
MSG.sysres.tableService.exportFinish=Hoàn thành xuất ra
##################################=
ERR.sysres.WebSocketDto.msgNull=Tin nhắn gửi không được để trống
ERR.sysres.AttachCopyBillVO.originTicketIdNull=Id chứng từ gốc không được để trống
ERR.sysres.AttachCopyBillVO.newTicketIdNull=Id chứng từ mới không được để trống
MSG.sysres.WebSocketCServer.mqSendFail=Phân phát thông tin MQ thất bại.
MSG.sysres.WebSocketCServer.heartbeatResponse=Phản ứng nhịp tim
MSG.sysres.PdfFileImpl.commonLogCount=Nguồn dữ liệu thống kê: Trường hợp sử dụng "Hệ thống EP - nhật ký thống nhất - phân tích hiệu suất giao diện"
MSG.sysres.PdfFileImpl.createBy=Được tạo bởi %s
MSG.sysres.PdfFileImpl.checker=Người kiểm tra
MSG.sysres.PdfFileImpl.checkTime=Thời gian kiểm tra
MSG.sysres.PdfFileImpl.countPeriod=Chu kỳ thống kê
MSG.sysres.PdfFileImpl.systemEnv=Môi trường hệ thống
MSG.sysres.PdfFileImpl.performanceCheckList=1. Danh sách kiểm tra hiệu suất giao diện
MSG.sysres.PdfFileImpl.averageCostTimeTop=Thời gian tiêu hao trung bình Top10
MSG.sysres.PdfFileImpl.serverName=Tên dịch vụ
MSG.sysres.PdfFileImpl.interfaceUri=Giao diện URI
MSG.sysres.PdfFileImpl.successRate=Tỉ lệ thành công
MSG.sysres.PdfFileImpl.averageCostTime=Thời gian tiêu hao trung bình (ms)
MSG.sysres.PdfFileImpl.runTimes=Số lượng thực hiện (lần)
MSG.sysres.PdfFileImpl.handler=Người xử lý
MSG.sysres.PdfFileImpl.handleTime=Thời gian xử lý
MSG.sysres.PdfFileImpl.remarks=Ghi chú
MSG.sysres.PdfFileImpl.costTimeTop=Thời gian tiêu hao Top10
MSG.sysres.PdfFileImpl.costTime=Thời gian thực hiện
MSG.sysres.PdfFileImpl.costTimems=Thời gian tiêu hao thực hiện (ms)
MSG.sysres.PdfFileImpl.exceptionTop=1.3. Bất thường dịch vụ Top10
MSG.sysres.PdfFileImpl.exceptionInfo=Thông tin bất thường
MSG.sysres.PdfFileImpl.exceptionAccount=Số lượng bất thường
MSG.sysres.PdfFileImpl.searchHelpPerformanceList=2. Danh sách kiểm tra hiệu suất  hỗ trợ tìm kiếm
MSG.sysres.PdfFileImpl.searchHelpName=Tên hỗ trợ tìm kiếm
MSG.sysres.PdfFileImpl.databasePerformanceList=3. Danh sách kiểm tra hiệu suất thực hiện cơ sở dữ liệu
MSG.sysres.PdfFileImpl.showSqlTop=3.1. SqlTop10 chậm
MSG.sysres.PdfFileImpl.showSqlCount=Nguồn dữ liệu thống kê: nhật ký cơ sở dữ liệu SQL chậm, trường hợp sử dụng "Hệ thống EP - nhật ký thống nhất - nhật ký SQL chậm"
MSG.sysres.PdfFileImpl.clientIp=IP đầu khách hàng
MSG.sysres.PdfFileImpl.databaseAddress=Địa chỉ cơ sở dữ liệu
MSG.sysres.PdfFileImpl.databaseInstance=Ví dụ thực tế cơ sở dữ liệu
MSG.sysres.PdfFileImpl.returnRecordCount=Số lượng ghi chép trả về
MSG.sysres.PdfFileImpl.quartzTask=4. Nhiệm vụ theo giờ
MSG.sysres.PdfFileImpl.exceptionCount=4.1. Thống kê bất thường Top10
MSG.sysres.PdfFileImpl.quartzLogCount=Nguồn dữ liệu thống kê: "Hệ thống EP - nhật ký thống nhất - Nhật ký nhiệm vụ theo giờ"
MSG.sysres.PdfFileImpl.name=Tên
MSG.sysres.PdfFileImpl.failTimes=Số lần thất bại
MSG.sysres.PdfFileImpl.execCountTop=4.2. Thống kê thực hiện Top10
MSG.sysres.PdfFileImpl.execTimes=Số lần thực hiện
MSG.sysres.MyHeaderFooter.pageStartEnd=Tôi là đầu trang/chân trang
MSG.sysres.MyHeaderFooter.currentPage=Trang thứ %s/
MSG.sysres.MyHeaderFooter.totalPage=Tổng %s trang
#############################=
ERR.message.MessageModuleEnum.mobile=Số điện thoại
ERR.message.MessageModuleEnum.emailAddress=Địa chỉ hộp thư
ERR.message.MessageModuleEnum.wxAccount=Số Wechat
ERR.message.MessageModuleEnum.dingAccount=Số DingTalk
ERR.message.DingTemplateDTO.messageTitleNull=Tiêu đề thông tin không được để trống!
ERR.message.DingTemplateDTO.messageContentNull=Nội dung thông tin không được để trống!
ERR.message.DingTemplateDTO.redirectUrlNull=URL nhảy không được để trống!
ERR.message.DingTemplateDTO.methodContent202=Nội dung của 202 phương thức!
ERR.message.MessageTemplateSendDTO.publishDateNull=Ngày phát hành không được để trống
ERR.message.MessageTemplateSendDTO.objectDetailNull=Chi tiết đối tượng không được để trống
ERR.message.MessageTemplateSendUserDTO.busiSysCodeNull=Mã số hệ thống kinh doanh không được để trống
ERR.message.SendDingDTO.dingIdNull=Id người dùng DingTalk không được để trống!
ERR.message.SendDingEPDTO.epsysIdNull=Id người dùng hệ thống EP không được để trống!
ERR.message.SendDingEPDTO.epsysCompanyIdNull=ID công ty của id người dùng hệ thống EP!
ERR.message.SendMailDTO.emailReceiverNull=Địa chỉ người nhận hộp thư không được để trống!
ERR.message.SendSmsDTO.smsReceiverNull=Địa chỉ người nhận tin nhắn không được để trống!
ERR.message.SendSmsDTO.templateCodeNull=Mã số mẫu không được để trống!
ERR.message.SendWxTemplateDTO.wxReceiverAddressNull=Địa chỉ người nhận Wechat không được để trống!
ERR.message.SendWxTemplateDTO.templateContentNull=Nội dung mẫu không được để trống!
ERR.message.VerificationDTO.mobileNull=Số điện thoại không được để trống!
ERR.message.MessageFactory.messageFactoryNoTemplate=Thông tin nhà máy chưa định nghĩa mẫu: