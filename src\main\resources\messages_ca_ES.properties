#这里填写西班牙语翻译
MSG.sysres.common.uploadSuccess=¡Carga exitosa!
ERR.sysres.common.uploadFail=¡Carga fallida!
MSG.sysres.common.copySuccess=¡Copiado con éxito!
MSG.sysres.common.msgSendSuccess=Mensaje enviado con éxito
MSG.sysres.websocket.setSender=¡Por favor, especifique el remitente!
MSG.sysres.pdfFileImpl.localTitle=Fuente de estadísticas: Ejemplares de "Sistema EP - Registro unificado - Análisis de rendimiento de la interfaz"нтерфейса»
ERR.sysres.tableService.getPageFail=Error en la ejecución de la paginación doExport para los datos.
MSG.sysres.tableService.serviceNotFound=No se ha encontrado el servicio %s.
MSG.sysres.tableService.enterDownload=Por favor, haga clic en %s para descargar el archivo.
MSG.sysres.tableService.exportFinish=Salida completada
##################################=
ERR.sysres.WebSocketDto.msgNull=El mensaje de envío no debe estar vacío
ERR.sysres.AttachCopyBillVO.originTicketIdNull=El ID del documento original no puede estar vacío
ERR.sysres.AttachCopyBillVO.newTicketIdNull=El ID del nuevo documento no debe estar vacío
MSG.sysres.WebSocketCServer.mqSendFail=La distribución de mensajes MQ ha fallado.
MSG.sysres.WebSocketCServer.heartbeatResponse=Respuesta de los latidos del corazón
MSG.sysres.PdfFileImpl.commonLogCount=Fuente de estadísticas: Ejemplares de "Sistema EP - Registro unificado - Análisis de rendimiento de la interfaz"ти интерфейса»
MSG.sysres.PdfFileImpl.createBy=Creado por %s
MSG.sysres.PdfFileImpl.checker=Inspector
MSG.sysres.PdfFileImpl.checkTime=Hora de inspección
MSG.sysres.PdfFileImpl.countPeriod=Periodo de estadísticas
MSG.sysres.PdfFileImpl.systemEnv=Entorno del sistema
MSG.sysres.PdfFileImpl.performanceCheckList=1. Lista de control del rendimiento de la interfaz
MSG.sysres.PdfFileImpl.averageCostTimeTop=Tiempo medio empleado Top 10
MSG.sysres.PdfFileImpl.serverName=Nombre del servicio
MSG.sysres.PdfFileImpl.interfaceUri=Interfaz URI
MSG.sysres.PdfFileImpl.successRate=Tasa de éxito
MSG.sysres.PdfFileImpl.averageCostTime=Tiempo medio empleado (ms)
MSG.sysres.PdfFileImpl.runTimes=Número de ejecuciones (veces)
MSG.sysres.PdfFileImpl.handler=Manipulador
MSG.sysres.PdfFileImpl.handleTime=Tiempo de procesamiento
MSG.sysres.PdfFileImpl.remarks=Observaciones
MSG.sysres.PdfFileImpl.costTimeTop=Consumo de tiempo Top10
MSG.sysres.PdfFileImpl.costTime=Hora de ejecución
MSG.sysres.PdfFileImpl.costTimems=Tiempo usado de ejecución (ms)
MSG.sysres.PdfFileImpl.exceptionTop=1.3. Los TOP 10 errores en el servicio
MSG.sysres.PdfFileImpl.exceptionInfo=Información de errores
MSG.sysres.PdfFileImpl.exceptionAccount=Número de errores
MSG.sysres.PdfFileImpl.searchHelpPerformanceList=2. Lista de comprobación del rendimiento de la ayuda a la búsqueda
MSG.sysres.PdfFileImpl.searchHelpName=Nombre de la ayuda de búsqueda
MSG.sysres.PdfFileImpl.databasePerformanceList=3. Lista de comprobación del rendimiento de la ejecución de la base de datos
MSG.sysres.PdfFileImpl.showSqlTop=3.1. Lento Sql Top10
MSG.sysres.PdfFileImpl.showSqlCount=Fuente de datos estadísticos: Registro de SQI lento en la base de datos y ejemplares de "Sistema EP-Registro unificado-Registro de SQl lento". журнал – журнал медленного SQL»
MSG.sysres.PdfFileImpl.clientIp=IP del cliente
MSG.sysres.PdfFileImpl.databaseAddress=Dirección de la base de datos
MSG.sysres.PdfFileImpl.databaseInstance=Casos de la base de datos
MSG.sysres.PdfFileImpl.returnRecordCount=Número de registros devueltos
MSG.sysres.PdfFileImpl.quartzTask=4. Tareas programadas
MSG.sysres.PdfFileImpl.exceptionCount=4.1. Los Top 10 errores estadísticos
MSG.sysres.PdfFileImpl.quartzLogCount=Fuente de datos estadísticos: "Sistema EP - Registro unificado - Registro de tareas programadas"
MSG.sysres.PdfFileImpl.name=Nombre
MSG.sysres.PdfFileImpl.failTimes=Número de fallos
MSG.sysres.PdfFileImpl.execCountTop=4.2. Las 10 principales estadísticas ejecutivas
MSG.sysres.PdfFileImpl.execTimes=Número de ejecuciones
MSG.sysres.MyHeaderFooter.pageStartEnd=Soy  Cabecera/Pie de página
MSG.sysres.MyHeaderFooter.currentPage=Página %s/
MSG.sysres.MyHeaderFooter.totalPage=Total de páginas %s
#############################=
ERR.message.MessageModuleEnum.mobile=Número de teléfono móvil
ERR.message.MessageModuleEnum.emailAddress=Dirección de correo electrónico
ERR.message.MessageModuleEnum.wxAccount=ID de WeChat
ERR.message.MessageModuleEnum.dingAccount=ID de DingTalk
ERR.message.DingTemplateDTO.messageTitleNull=¡El título del mensaje no debe estar vacío!
ERR.message.DingTemplateDTO.messageContentNull=¡El contenido del mensaje no debe estar vacío!
ERR.message.DingTemplateDTO.redirectUrlNull=¡La URL de salto no debe estar vacía!
ERR.message.DingTemplateDTO.methodContent202=¡Contenido del modo 202!
ERR.message.MessageTemplateSendDTO.publishDateNull=¡La fecha de publicación no debe estar vacía!
ERR.message.MessageTemplateSendDTO.objectDetailNull=Los detalles del objeto no deben estar vacíos.
ERR.message.MessageTemplateSendUserDTO.busiSysCodeNull=El número del sistema empresarial no puede estar vacío.
ERR.message.SendDingDTO.dingIdNull=¡El ID de usuario de DingTalk no puede estar vacía!
ERR.message.SendDingEPDTO.epsysIdNull=¡El ID de usuario del sistema EP no debe estar vacía!
ERR.message.SendDingEPDTO.epsysCompanyIdNull=¡El ID de usuario del sistema EP e ID de su empresa!
ERR.message.SendMailDTO.emailReceiverNull=¡La dirección del destinatario del correo electrónico no debe estar vacía!
ERR.message.SendSmsDTO.smsReceiverNull=¡La dirección del destinatario del SMS no debe estar vacía!
ERR.message.SendSmsDTO.templateCodeNull=¡El número de la plantilla no puede estar vacío!
ERR.message.SendWxTemplateDTO.wxReceiverAddressNull=¡La dirección del destinatario de WeChat no puede estar vacía!
ERR.message.SendWxTemplateDTO.templateContentNull=¡El contenido de la plantilla no debe estar vacío!
ERR.message.VerificationDTO.mobileNull=¡El número de teléfono no puede estar en blanco!
ERR.message.MessageFactory.messageFactoryNoTemplate=Plantilla no definida de la fábrica de mensajes: