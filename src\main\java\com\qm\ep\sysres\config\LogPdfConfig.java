package com.qm.ep.sysres.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * ly 20210907
 * pdf文件配置信息
 * 配置信息的存放位置为yml文件
 */
@Data
@ConfigurationProperties(prefix = "qm.ftp")
public class LogPdfConfig {
    private Integer limit;
    private String logServerName;
    private String tsortby;
    private String tsortbyslow;
    private long currentPage;
    private long pageSize;
    private List<String> logMsgSub;
    private List<String> statusList;
    private String title;
    private String author;
    private String subject;
    private String keywords;
    private String creator;
    private String watermark;
    private String pdfname;
    private String pdfcreateman;
    private String pdfev;
}