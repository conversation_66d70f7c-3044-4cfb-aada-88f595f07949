package com.qm.ep.sysres.config;

import com.qm.ep.sysres.Interceptor.HttpResponseSecurityInterceptor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/7
 */
@Configuration
@EnableConfigurationProperties(LogPdfConfig.class)
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HttpResponseSecurityInterceptor()).addPathPatterns("/**");
    }
}
