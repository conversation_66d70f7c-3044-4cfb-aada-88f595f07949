package com.qm.ep.sysres.utils;

import com.qm.tds.api.exception.QmException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientException;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.ConnectException;

/**
 * HTTP客户端异常处理器
 * 专门处理JDK17升级后的HTTP连接异常
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Component
@Slf4j
public class HttpClientExceptionHandler {

    /**
     * 处理HTTP请求异常
     * 
     * @param e 异常对象
     * @param url 请求URL
     * @param pageIndex 页码
     * @return 处理后的异常信息
     */
    public String handleHttpException(Exception e, String url, int pageIndex) {
        String errorMessage;
        
        if (e instanceof ResourceAccessException) {
            ResourceAccessException rae = (ResourceAccessException) e;
            Throwable cause = rae.getCause();
            
            if (cause instanceof SocketTimeoutException) {
                errorMessage = String.format("请求超时 - URL: %s, 页码: %d, 建议检查网络连接或增加超时时间", url, pageIndex);
                log.error("---error--Socket超时异常: {}", errorMessage, e);
            } else if (cause instanceof ConnectException) {
                errorMessage = String.format("连接失败 - URL: %s, 页码: %d, 目标服务可能不可用", url, pageIndex);
                log.error("---error--连接异常: {}", errorMessage, e);
            } else if (cause instanceof IOException) {
                String ioMessage = cause.getMessage();
                if (ioMessage != null && ioMessage.contains("insufficient data written")) {
                    errorMessage = String.format("数据传输不完整 - URL: %s, 页码: %d, 这是JDK17兼容性问题，请检查HTTP客户端配置", url, pageIndex);
                    log.error("---error--JDK17兼容性问题 - 数据传输不完整: {}", errorMessage, e);
                } else if (ioMessage != null && ioMessage.contains("Connection has been closed")) {
                    errorMessage = String.format("连接被关闭 - URL: %s, 页码: %d, 这是JDK17兼容性问题，建议重试", url, pageIndex);
                    log.error("---error--JDK17兼容性问题 - 连接被关闭: {}", errorMessage, e);
                } else {
                    errorMessage = String.format("IO异常 - URL: %s, 页码: %d, 错误: %s", url, pageIndex, ioMessage);
                    log.error("---error--IO异常: {}", errorMessage, e);
                }
            } else {
                errorMessage = String.format("网络访问异常 - URL: %s, 页码: %d, 错误: %s", url, pageIndex, rae.getMessage());
                log.error("---error--网络访问异常: {}", errorMessage, e);
            }
        } else if (e instanceof RestClientException) {
            errorMessage = String.format("REST客户端异常 - URL: %s, 页码: %d, 错误: %s", url, pageIndex, e.getMessage());
            log.error("---error--REST客户端异常: {}", errorMessage, e);
        } else {
            errorMessage = String.format("未知异常 - URL: %s, 页码: %d, 错误: %s", url, pageIndex, e.getMessage());
            log.error("---error--未知异常: {}", errorMessage, e);
        }
        
        return errorMessage;
    }

    /**
     * 判断是否为JDK17兼容性相关的异常
     * 
     * @param e 异常对象
     * @return true如果是JDK17兼容性问题
     */
    public boolean isJdk17CompatibilityIssue(Exception e) {
        if (e instanceof ResourceAccessException) {
            ResourceAccessException rae = (ResourceAccessException) e;
            Throwable cause = rae.getCause();
            
            if (cause instanceof IOException) {
                String message = cause.getMessage();
                return message != null && (
                    message.contains("insufficient data written") ||
                    message.contains("Connection has been closed") ||
                    message.contains("The body is not set")
                );
            }
        }
        return false;
    }

    /**
     * 创建JDK17兼容性异常
     * 
     * @param originalException 原始异常
     * @param url 请求URL
     * @param pageIndex 页码
     * @return QmException
     */
    public QmException createJdk17CompatibilityException(Exception originalException, String url, int pageIndex) {
        String message = handleHttpException(originalException, url, pageIndex);
        
        if (isJdk17CompatibilityIssue(originalException)) {
            message += " [JDK17兼容性问题：建议检查HTTP客户端配置，确保使用了正确的超时设置和连接池配置]";
        }
        
        return new QmException(message, originalException);
    }

    /**
     * 获取重试建议
     * 
     * @param e 异常对象
     * @return 重试建议信息
     */
    public String getRetryAdvice(Exception e) {
        if (isJdk17CompatibilityIssue(e)) {
            return "建议：1) 检查HTTP客户端超时配置 2) 确保连接池设置正确 3) 验证目标服务状态 4) 考虑减少请求数据量";
        } else if (e instanceof ResourceAccessException) {
            ResourceAccessException rae = (ResourceAccessException) e;
            Throwable cause = rae.getCause();
            
            if (cause instanceof SocketTimeoutException) {
                return "建议：1) 增加超时时间 2) 检查网络连接 3) 验证目标服务响应时间";
            } else if (cause instanceof ConnectException) {
                return "建议：1) 检查目标服务是否运行 2) 验证网络连通性 3) 确认服务地址和端口正确";
            }
        }
        
        return "建议：1) 检查网络连接 2) 验证服务状态 3) 查看详细错误日志";
    }
}
