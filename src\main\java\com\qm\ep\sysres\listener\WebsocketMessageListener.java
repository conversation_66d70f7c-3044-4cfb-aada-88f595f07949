package com.qm.ep.sysres.listener;

import com.alibaba.fastjson.JSON;
import com.qm.ep.sysres.constant.SocketConstant;
import com.qm.ep.sysres.domain.dto.WebSocketDto;
import com.qm.ep.sysres.server.WebSocketCServer;
import com.qm.tds.mq.builder.MessageStruct;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class WebsocketMessageListener {

    @Autowired
    private WebSocketCServer webSocketServer;


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = SocketConstant.QUEUE_NAME_WEBSOCKET + "-${qm.websocket.number}",
                    arguments = {@Argument(name = SocketConstant.RABBIT_DEADLETTER_EXCHANGE, value = SocketConstant.RABBIT_SOCKET_DEADLETTER_EXCHANGE),
                            @Argument(name = SocketConstant.RABBIT_DEADLETTER_ROUTINGKEY, value = SocketConstant.RABBIT_SOCKET_DEADLETTER_ROUTINGKEY)}),
            exchange = @Exchange(value = SocketConstant.WEBSOCKET_EXCHANGE, type = ExchangeTypes.FANOUT)))
    public void directHandlerManualAck(MessageStruct messageStruct, Message message, Channel channel) {
        log.info("广播队列1，手动ACK，接收消息：{}", JSON.toJSONString(messageStruct));
        final long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            // 处理消息
            String msgStr = JSON.toJSONString(messageStruct.getMessage());
            WebSocketDto webSocketDto = JSON.parseObject(msgStr, WebSocketDto.class);
            webSocketServer.pushMessage(webSocketDto.getUserStamp(), msgStr);
            //消息手动ACK
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.info("---error--"+"监听确认信息异常，消息通道非法关闭！", e);
        }
    }


    @RabbitListener(bindings = @QueueBinding(
            key = SocketConstant.RABBIT_SOCKET_DEADLETTER_ROUTINGKEY,
            value = @Queue(value = SocketConstant.RABBIT_SOCKET_DEADLETTER_QUEUE),
            exchange = @Exchange(value = SocketConstant.RABBIT_SOCKET_DEADLETTER_EXCHANGE)))
    public void deadLetterConsumer(MessageStruct messageStruct, Message message, Channel channel) {
        //  如果手动ACK,消息会被监听消费,但是消息在队列中依旧存在,如果 未配置 acknowledge-mode 默认是会在消费完毕后自动ACK掉
        final long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            //手动ACK
            channel.basicAck(deliveryTag, false);
            log.info("接收到死信消息并自动签收:[{}]", JSON.toJSONString(messageStruct));
        } catch (Exception e) {
            log.info("---error--"+"死信队列消息签收失败", e);
        }
    }
}
