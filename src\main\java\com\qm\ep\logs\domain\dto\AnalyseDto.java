package com.qm.ep.logs.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.qm.tds.api.domain.JsonParamDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: es 接口性能分析实体
 * @author: Cyl
 * @time: 2020/07/29 12:55
 */
@Data
@SuppressWarnings("squid:S1068")
public class AnalyseDto extends JsonParamDto implements Serializable {

    private static final long serialVersionUID = 2073684414842102685L;
    /**
     * id
     */
    private String id;


    /**
     * 接口地址
     */
    @JsonAlias("URI")
    private String uri;


    /**
     * 消耗时间
     */
    @JsonAlias("TIME_CONSUMING")
    private String timeConsuming;


    /**
     * 请求服务名称
     */
    @JsonAlias("SERVICE_NAME")
    private String serviceName;

    /**
     * 自定义名称
     */
    @JsonAlias("LOG_SERVER_NAME")
    private String logServerName;


    /**
     * 访问时间 开始时间
     */
    private String startTime;

    /**
     * 访问时间 结束时间
     */
    private String endTime;

    /**
     * 命中数
     */
    private Integer limit;
    /**
     * 时间索引
     */
    private List<String> searchTimeList;

}
